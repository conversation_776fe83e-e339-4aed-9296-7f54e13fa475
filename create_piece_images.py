import pygame
import sys
import os

# Initialize pygame
pygame.init()

# Constants
PIECE_SIZE = 80
PIECE_WHITE = (255, 255, 255)
PIECE_BLACK = (0, 0, 0)
TRANSPARENT = (0, 0, 0, 0)

def create_piece_image(piece_type, color, filename):
    """Create a piece image and save it as PNG"""
    surface = pygame.Surface((PIECE_SIZE, PIECE_SIZE), pygame.SRCALPHA)
    surface.fill(TRANSPARENT)
    
    center_x = PIECE_SIZE // 2
    center_y = PIECE_SIZE // 2
    piece_color = PIECE_WHITE if color == 'w' else PIECE_BLACK
    outline_color = PIECE_BLACK if color == 'w' else PIECE_WHITE
    
    if piece_type == 'p':  # Pawn
        pygame.draw.circle(surface, piece_color, (center_x, center_y - 8), 12)
        pygame.draw.circle(surface, outline_color, (center_x, center_y - 8), 12, 2)
        pygame.draw.rect(surface, piece_color, (center_x - 10, center_y + 4, 20, 6))
        pygame.draw.rect(surface, outline_color, (center_x - 10, center_y + 4, 20, 6), 2)
    
    elif piece_type == 'r':  # Rook
        pygame.draw.rect(surface, piece_color, (center_x - 12, center_y - 12, 24, 24))
        pygame.draw.rect(surface, outline_color, (center_x - 12, center_y - 12, 24, 24), 2)
        # Crenellations
        for i in range(3):
            pygame.draw.rect(surface, piece_color, (center_x - 10 + i * 6, center_y - 16, 5, 6))
            pygame.draw.rect(surface, outline_color, (center_x - 10 + i * 6, center_y - 16, 5, 6), 1)
    
    elif piece_type == 'n':  # Knight
        points = [
            (center_x - 8, center_y + 12),
            (center_x - 12, center_y),
            (center_x - 6, center_y - 12),
            (center_x + 4, center_y - 10),
            (center_x + 12, center_y - 4),
            (center_x + 10, center_y + 12)
        ]
        pygame.draw.polygon(surface, piece_color, points)
        pygame.draw.polygon(surface, outline_color, points, 2)
    
    elif piece_type == 'b':  # Bishop
        pygame.draw.circle(surface, piece_color, (center_x, center_y + 4), 10)
        pygame.draw.circle(surface, outline_color, (center_x, center_y + 4), 10, 2)
        points = [(center_x, center_y - 12), (center_x - 6, center_y + 4), (center_x + 6, center_y + 4)]
        pygame.draw.polygon(surface, piece_color, points)
        pygame.draw.polygon(surface, outline_color, points, 2)
        pygame.draw.circle(surface, piece_color, (center_x, center_y - 12), 2)
    
    elif piece_type == 'q':  # Queen
        pygame.draw.circle(surface, piece_color, (center_x, center_y + 4), 12)
        pygame.draw.circle(surface, outline_color, (center_x, center_y + 4), 12, 2)
        # Crown points
        for i in range(5):
            x_pos = center_x - 10 + i * 5
            height = 6 if i % 2 == 0 else 10
            pygame.draw.rect(surface, piece_color, (x_pos, center_y - 12, 3, height))
            pygame.draw.rect(surface, outline_color, (x_pos, center_y - 12, 3, height), 1)
    
    elif piece_type == 'k':  # King
        pygame.draw.circle(surface, piece_color, (center_x, center_y + 4), 12)
        pygame.draw.circle(surface, outline_color, (center_x, center_y + 4), 12, 2)
        # Cross
        pygame.draw.rect(surface, piece_color, (center_x - 1, center_y - 14, 3, 10))
        pygame.draw.rect(surface, piece_color, (center_x - 5, center_y - 11, 10, 3))
        pygame.draw.rect(surface, outline_color, (center_x - 1, center_y - 14, 3, 10), 1)
        pygame.draw.rect(surface, outline_color, (center_x - 5, center_y - 11, 10, 3), 1)
    
    # Save the image
    pygame.image.save(surface, filename)
    print(f"Created {filename}")

def main():
    # Create pieces directory if it doesn't exist
    if not os.path.exists('pieces'):
        os.makedirs('pieces')
    
    pieces = ['p', 'r', 'n', 'b', 'q', 'k']
    colors = ['w', 'b']
    
    for color in colors:
        for piece in pieces:
            filename = f"pieces/{color}{piece}.png"
            create_piece_image(piece, color, filename)
    
    print("All piece images created successfully!")
    pygame.quit()

if __name__ == "__main__":
    main()
