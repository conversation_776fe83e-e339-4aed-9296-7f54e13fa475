"""
Enhanced 2D Chess Game with AI, Timer System, and Advanced UI

This is a complete chess game implementation featuring:
- Human vs Human and Human vs AI game modes
- 30-minute timer system for each player
- Undo/redo functionality with complete move history
- Beautiful graphical interface with sidebar controls
- Free movement system (no check restrictions)
- Exit confirmation dialogs
- Custom piece graphics with fallback to Unicode symbols

Author: AI Assistant
Date: 2025
"""

import pygame  # Main game library for graphics and input
import sys     # System functions for exit
import random  # For AI move randomization
import math    # Mathematical functions (if needed)

# ==================== GAME CONSTANTS ====================

# Board and window dimensions
BOARD_SIZE = 640        # Size of the chess board in pixels
SIDEBAR_WIDTH = 200     # Width of the control sidebar
WIDTH, HEIGHT = BOARD_SIZE + SIDEBAR_WIDTH, BOARD_SIZE + 50  # Total window size
ROWS, COLS = 8, 8       # Standard chess board is 8x8
SQUARE_SIZE = BOARD_SIZE // COLS  # Size of each chess square

# Color scheme for the game interface
WHITE = (240, 217, 181)      # Light squares on chess board
BLACK = (181, 136, 99)       # Dark squares on chess board
HIGHLIGHT = (186, 202, 68)   # Selected piece highlight color
VALID_MOVE = (144, 238, 144) # Valid move squares color (light green)
PIECE_WHITE = (255, 255, 255) # White piece color
PIECE_BLACK = (0, 0, 0)      # Black piece color
TEXT_COLOR = (50, 50, 50)    # Main text color (dark gray)
MENU_BG = (200, 200, 200)    # Menu background color
BUTTON_COLOR = (70, 130, 180) # Normal button color (steel blue)
BUTTON_HOVER = (100, 149, 237) # Button hover color (dodger blue)
BUTTON_DISABLED = (169, 169, 169) # Disabled button color (gray)
SIDEBAR_BG = (245, 245, 245) # Sidebar background color (light gray)
BORDER_COLOR = (100, 100, 100) # Border color for UI elements
SUCCESS_COLOR = (34, 139, 34)  # Success/positive color (forest green)
WARNING_COLOR = (255, 140, 0)  # Warning color (dark orange)
ERROR_COLOR = (220, 20, 60)    # Error/negative color (crimson)

# ==================== GAME MODE CONSTANTS ====================

# Available game modes for player selection
HUMAN_VS_HUMAN = 1  # Two human players on same computer
HUMAN_VS_AI = 2     # Human player vs AI opponent

# ==================== CHESS PIECE DEFINITIONS ====================

# Unicode symbols for chess pieces (used for visual display)
# Format: 'color+piece_type': 'unicode_symbol'
# Colors: w=white, b=black
# Pieces: p=pawn, r=rook, n=knight, b=bishop, q=queen, k=king
PIECE_SYMBOLS = {
    'wp': '♙', 'wr': '♖', 'wn': '♘', 'wb': '♗', 'wq': '♕', 'wk': '♔',  # White pieces
    'bp': '♟', 'br': '♜', 'bn': '♞', 'bb': '♝', 'bq': '♛', 'bk': '♚'   # Black pieces
}

# Piece values for AI evaluation (used in minimax algorithm)
# Higher values = more valuable pieces
# King has highest value to prioritize king safety
PIECE_VALUES = {
    'p': 1,    # Pawn - basic unit
    'n': 3,    # Knight - moderate value
    'b': 3,    # Bishop - moderate value
    'r': 5,    # Rook - high value
    'q': 9,    # Queen - highest value piece
    'k': 100   # King - invaluable (game ends if captured)
}

# ==================== MOVE HISTORY SYSTEM ====================

class MoveHistory:
    """
    Manages the complete history of moves for undo/redo functionality.

    This class maintains a list of all moves made during the game and provides
    methods to undo and redo moves. It supports branching history - if you undo
    several moves and then make a new move, the "future" moves are discarded.

    Each move contains:
    - board_before: Board state before the move
    - board_after: Board state after the move
    - player_before: Which player made the move
    - player_after: Which player's turn it is after the move
    - from_pos: Starting position of the piece
    - to_pos: Ending position of the piece
    - captured_piece: What piece (if any) was captured
    """

    def __init__(self):
        """Initialize empty move history."""
        self.moves = []           # List of all moves made
        self.current_index = -1   # Index of current position in history (-1 = no moves)

    def add_move(self, move_data):
        """
        Add a new move to the history.

        Args:
            move_data (dict): Complete information about the move

        Note: This removes any "future" moves if we're not at the end of history
        (handles the case where user undoes moves then makes a new move)
        """
        # Remove any moves after current index (branching history)
        self.moves = self.moves[:self.current_index + 1]
        # Add the new move
        self.moves.append(move_data)
        # Update current position
        self.current_index += 1

    def can_undo(self):
        """Check if there are moves available to undo."""
        return self.current_index >= 0

    def can_redo(self):
        """Check if there are moves available to redo."""
        return self.current_index < len(self.moves) - 1

    def undo(self):
        """
        Undo the last move and return the move data.

        Returns:
            dict: Move data for the undone move, or None if no moves to undo
        """
        if self.can_undo():
            move = self.moves[self.current_index]
            self.current_index -= 1  # Move back in history
            return move
        return None

    def redo(self):
        """
        Redo the next move and return the move data.

        Returns:
            dict: Move data for the redone move, or None if no moves to redo
        """
        if self.can_redo():
            self.current_index += 1  # Move forward in history
            return self.moves[self.current_index]
        return None

    def clear(self):
        """Clear all move history (used when starting new game)."""
        self.moves = []
        self.current_index = -1

# ==================== CHESS TIMER SYSTEM ====================

class ChessTimer:
    """
    Manages the chess game timer system with separate timers for each player.

    This class implements a tournament-style chess timer where each player has
    a fixed amount of time (default 30 minutes) for the entire game. The timer
    only counts down for the active player and switches when moves are made.

    Features:
    - Separate timers for white and black players
    - Automatic switching between players
    - Color-coded display based on remaining time
    - Pause/resume functionality for dialogs
    - Time-up detection for game ending
    """

    def __init__(self, initial_time_minutes=30):
        """
        Initialize the chess timer.

        Args:
            initial_time_minutes (int): Starting time for each player in minutes
        """
        # Convert minutes to milliseconds for precise timing
        self.initial_time = initial_time_minutes * 60 * 1000
        self.white_time = self.initial_time  # White player's remaining time
        self.black_time = self.initial_time  # Black player's remaining time
        self.last_update = pygame.time.get_ticks()  # Last time we updated the timer
        self.active = False          # Whether timer is currently running
        self.current_player = 'w'    # Which player's timer is active

    def start_timer(self, player):
        """
        Start the timer for a specific player.

        Args:
            player (str): 'w' for white, 'b' for black
        """
        self.current_player = player
        self.active = True
        self.last_update = pygame.time.get_ticks()

    def pause_timer(self):
        """
        Pause the timer (used during dialogs or game over).
        Updates the current time before pausing to ensure accuracy.
        """
        if self.active:
            self.update_time()  # Make sure time is current
            self.active = False

    def switch_player(self, new_player):
        """
        Switch the timer to the other player.

        Args:
            new_player (str): 'w' for white, 'b' for black

        This is called when a move is made to switch whose timer is counting down.
        """
        if self.active:
            self.update_time()  # Update current player's time first
        self.current_player = new_player
        self.last_update = pygame.time.get_ticks()  # Reset the update timestamp

    def update_time(self):
        """
        Update the current player's remaining time.

        This calculates how much time has elapsed since the last update
        and subtracts it from the current player's time. Called every frame
        when the timer is active.
        """
        if not self.active:
            return

        current_time = pygame.time.get_ticks()
        elapsed = current_time - self.last_update  # Time elapsed in milliseconds

        # Subtract elapsed time from current player's timer
        if self.current_player == 'w':
            self.white_time = max(0, self.white_time - elapsed)
        else:
            self.black_time = max(0, self.black_time - elapsed)

        self.last_update = current_time

    def get_time_string(self, player):
        """
        Get formatted time string for display.

        Args:
            player (str): 'w' for white, 'b' for black

        Returns:
            str: Time formatted as "MM:SS" (e.g., "05:30")
        """
        time_ms = self.white_time if player == 'w' else self.black_time
        total_seconds = time_ms // 1000  # Convert to seconds
        minutes = total_seconds // 60
        seconds = total_seconds % 60
        return f"{minutes:02d}:{seconds:02d}"

    def is_time_up(self, player):
        """
        Check if a player has run out of time.

        Args:
            player (str): 'w' for white, 'b' for black

        Returns:
            bool: True if player's time is 0 or less
        """
        time_ms = self.white_time if player == 'w' else self.black_time
        return time_ms <= 0

    def get_time_color(self, player):
        """
        Get appropriate color for time display based on remaining time.

        Args:
            player (str): 'w' for white, 'b' for black

        Returns:
            tuple: RGB color tuple for display
            - Green: More than 1 minute remaining
            - Orange: 30-60 seconds remaining
            - Red: Less than 30 seconds remaining
        """
        time_ms = self.white_time if player == 'w' else self.black_time
        total_seconds = time_ms // 1000

        if total_seconds <= 30:      # Critical time - red alert
            return ERROR_COLOR
        elif total_seconds <= 60:   # Warning time - orange alert
            return WARNING_COLOR
        else:                       # Normal time - green
            return SUCCESS_COLOR

    def reset(self, initial_time_minutes=30):
        """
        Reset both timers to initial values.

        Args:
            initial_time_minutes (int): Starting time for each player in minutes

        Used when starting a new game.
        """
        self.initial_time = initial_time_minutes * 60 * 1000
        self.white_time = self.initial_time
        self.black_time = self.initial_time
        self.active = False
        self.current_player = 'w'

# ==================== BOARD SETUP AND DISPLAY ====================

def create_board():
    """
    Create the initial chess board setup with all pieces in starting positions.

    Returns:
        list: 8x8 2D list representing the chess board

    Board representation:
    - Empty squares: ""
    - Pieces: two-character strings (color + piece_type)
        - Colors: 'w' = white, 'b' = black
        - Pieces: 'p' = pawn, 'r' = rook, 'n' = knight, 'b' = bishop, 'q' = queen, 'k' = king

    Standard chess starting position:
    Row 0: Black back rank (rook, knight, bishop, queen, king, bishop, knight, rook)
    Row 1: Black pawns
    Rows 2-5: Empty squares
    Row 6: White pawns
    Row 7: White back rank (rook, knight, bishop, queen, king, bishop, knight, rook)
    """
    board = [
        ["br", "bn", "bb", "bq", "bk", "bb", "bn", "br"],   # Row 0: Black back rank
        ["bp"] * 8,                                         # Row 1: Black pawns
        [""] * 8,                                           # Row 2: Empty
        [""] * 8,                                           # Row 3: Empty
        [""] * 8,                                           # Row 4: Empty
        [""] * 8,                                           # Row 5: Empty
        ["wp"] * 8,                                         # Row 6: White pawns
        ["wr", "wn", "wb", "wq", "wk", "wb", "wn", "wr"]    # Row 7: White back rank
    ]
    return board

# Draw board with coordinates
def draw_board(win):
    # Draw squares
    for row in range(ROWS):
        for col in range(COLS):
            color = WHITE if (row + col) % 2 == 0 else BLACK
            pygame.draw.rect(win, color, (col*SQUARE_SIZE, row*SQUARE_SIZE, SQUARE_SIZE, SQUARE_SIZE))

    # Draw board border
    pygame.draw.rect(win, BORDER_COLOR, (0, 0, BOARD_SIZE, BOARD_SIZE), 3)

    # Draw coordinates
    coord_font = pygame.font.Font(None, 24)

    # Files (a-h)
    for col in range(COLS):
        letter = chr(ord('a') + col)
        text = coord_font.render(letter, True, TEXT_COLOR)
        win.blit(text, (col * SQUARE_SIZE + SQUARE_SIZE - 15, BOARD_SIZE + 5))

    # Ranks (1-8)
    for row in range(ROWS):
        number = str(8 - row)
        text = coord_font.render(number, True, TEXT_COLOR)
        win.blit(text, (5, row * SQUARE_SIZE + 5))

# Load piece images
PIECE_IMAGES = {}
def load_piece_images():
    pieces = ['wp', 'wr', 'wn', 'wb', 'wq', 'wk',
              'bp', 'br', 'bn', 'bb', 'bq', 'bk']
    for piece in pieces:
        try:
            image = pygame.image.load(f"pieces/{piece}.png")
            PIECE_IMAGES[piece] = pygame.transform.scale(image, (SQUARE_SIZE, SQUARE_SIZE))
        except pygame.error:
            # Fallback to drawn pieces if images don't exist
            PIECE_IMAGES[piece] = None

# Draw pieces with images or fallback graphics
def draw_pieces(win, board):
    for row in range(ROWS):
        for col in range(COLS):
            piece = board[row][col]
            if piece != "":
                if PIECE_IMAGES.get(piece):
                    # Use loaded image
                    win.blit(PIECE_IMAGES[piece], (col * SQUARE_SIZE, row * SQUARE_SIZE))
                else:
                    # Fallback to drawn piece
                    draw_piece_icon(win, piece, col * SQUARE_SIZE, row * SQUARE_SIZE)

# Draw individual piece with custom graphics
def draw_piece_icon(win, piece, x, y):
    center_x = x + SQUARE_SIZE // 2
    center_y = y + SQUARE_SIZE // 2
    piece_color = PIECE_WHITE if piece[0] == 'w' else PIECE_BLACK
    outline_color = PIECE_BLACK if piece[0] == 'w' else PIECE_WHITE

    piece_type = piece[1]

    if piece_type == 'p':  # Pawn
        # Draw pawn as circle with small base
        pygame.draw.circle(win, piece_color, (center_x, center_y - 10), 15)
        pygame.draw.circle(win, outline_color, (center_x, center_y - 10), 15, 2)
        pygame.draw.rect(win, piece_color, (center_x - 12, center_y + 5, 24, 8))
        pygame.draw.rect(win, outline_color, (center_x - 12, center_y + 5, 24, 8), 2)

    elif piece_type == 'r':  # Rook
        # Draw rook as castle tower
        pygame.draw.rect(win, piece_color, (center_x - 15, center_y - 15, 30, 30))
        pygame.draw.rect(win, outline_color, (center_x - 15, center_y - 15, 30, 30), 2)
        # Crenellations
        for i in range(3):
            pygame.draw.rect(win, piece_color, (center_x - 12 + i * 8, center_y - 20, 6, 8))
            pygame.draw.rect(win, outline_color, (center_x - 12 + i * 8, center_y - 20, 6, 8), 1)

    elif piece_type == 'n':  # Knight
        # Draw knight as horse head shape
        points = [
            (center_x - 10, center_y + 15),
            (center_x - 15, center_y),
            (center_x - 8, center_y - 15),
            (center_x + 5, center_y - 12),
            (center_x + 15, center_y - 5),
            (center_x + 12, center_y + 15)
        ]
        pygame.draw.polygon(win, piece_color, points)
        pygame.draw.polygon(win, outline_color, points, 2)

    elif piece_type == 'b':  # Bishop
        # Draw bishop as pointed hat
        pygame.draw.circle(win, piece_color, (center_x, center_y + 5), 12)
        pygame.draw.circle(win, outline_color, (center_x, center_y + 5), 12, 2)
        points = [(center_x, center_y - 15), (center_x - 8, center_y + 5), (center_x + 8, center_y + 5)]
        pygame.draw.polygon(win, piece_color, points)
        pygame.draw.polygon(win, outline_color, points, 2)
        pygame.draw.circle(win, piece_color, (center_x, center_y - 15), 3)

    elif piece_type == 'q':  # Queen
        # Draw queen with crown
        pygame.draw.circle(win, piece_color, (center_x, center_y + 5), 15)
        pygame.draw.circle(win, outline_color, (center_x, center_y + 5), 15, 2)
        # Crown points
        for i in range(5):
            x_pos = center_x - 12 + i * 6
            height = 8 if i % 2 == 0 else 12
            pygame.draw.rect(win, piece_color, (x_pos, center_y - 15, 4, height))
            pygame.draw.rect(win, outline_color, (x_pos, center_y - 15, 4, height), 1)

    elif piece_type == 'k':  # King
        # Draw king with cross crown
        pygame.draw.circle(win, piece_color, (center_x, center_y + 5), 15)
        pygame.draw.circle(win, outline_color, (center_x, center_y + 5), 15, 2)
        # Cross
        pygame.draw.rect(win, piece_color, (center_x - 2, center_y - 18, 4, 12))
        pygame.draw.rect(win, piece_color, (center_x - 6, center_y - 14, 12, 4))
        pygame.draw.rect(win, outline_color, (center_x - 2, center_y - 18, 4, 12), 1)
        pygame.draw.rect(win, outline_color, (center_x - 6, center_y - 14, 12, 4), 1)

# Check if a move is valid (basic piece movement rules)
def is_valid_basic_move(board, start_row, start_col, end_row, end_col):
    piece = board[start_row][start_col]
    if piece == "":
        return False

    # Can't capture own piece
    target = board[end_row][end_col]
    if target != "" and target[0] == piece[0]:
        return False

    piece_type = piece[1]
    row_diff = end_row - start_row
    col_diff = end_col - start_col

    # Pawn moves
    if piece_type == 'p':
        direction = -1 if piece[0] == 'w' else 1
        start_row_pawn = 6 if piece[0] == 'w' else 1

        # Forward move
        if col_diff == 0:
            if row_diff == direction and target == "":
                return True
            # Initial two-square move
            if start_row == start_row_pawn and row_diff == 2 * direction and target == "":
                return True
        # Diagonal capture
        elif abs(col_diff) == 1 and row_diff == direction and target != "":
            return True

    # Rook moves
    elif piece_type == 'r':
        if row_diff == 0 or col_diff == 0:
            return is_path_clear(board, start_row, start_col, end_row, end_col)

    # Bishop moves
    elif piece_type == 'b':
        if abs(row_diff) == abs(col_diff):
            return is_path_clear(board, start_row, start_col, end_row, end_col)

    # Queen moves (combination of rook and bishop)
    elif piece_type == 'q':
        if row_diff == 0 or col_diff == 0 or abs(row_diff) == abs(col_diff):
            return is_path_clear(board, start_row, start_col, end_row, end_col)

    # King moves
    elif piece_type == 'k':
        if abs(row_diff) <= 1 and abs(col_diff) <= 1:
            return True

    # Knight moves
    elif piece_type == 'n':
        if (abs(row_diff) == 2 and abs(col_diff) == 1) or (abs(row_diff) == 1 and abs(col_diff) == 2):
            return True

    return False

# Check if a move is valid (basic piece movement rules only)
def is_valid_move(board, start_row, start_col, end_row, end_col):
    # Only check basic move validity - no check validation
    return is_valid_basic_move(board, start_row, start_col, end_row, end_col)

# Check if path is clear for sliding pieces
def is_path_clear(board, start_row, start_col, end_row, end_col):
    row_step = 0 if start_row == end_row else (1 if end_row > start_row else -1)
    col_step = 0 if start_col == end_col else (1 if end_col > start_col else -1)

    current_row, current_col = start_row + row_step, start_col + col_step

    while current_row != end_row or current_col != end_col:
        if board[current_row][current_col] != "":
            return False
        current_row += row_step
        current_col += col_step

    return True

# Get valid moves for highlighting
def get_valid_moves(board, row, col):
    valid_moves = []
    for end_row in range(ROWS):
        for end_col in range(COLS):
            if is_valid_move(board, row, col, end_row, end_col):
                valid_moves.append((end_row, end_col))
    return valid_moves

# Get all valid moves for a player
def get_all_valid_moves(board, player):
    moves = []
    for row in range(ROWS):
        for col in range(COLS):
            piece = board[row][col]
            if piece != "" and piece[0] == player:
                valid_moves = get_valid_moves(board, row, col)
                for end_row, end_col in valid_moves:
                    moves.append(((row, col), (end_row, end_col)))
    return moves

# Simple board evaluation for AI
def evaluate_board(board):
    score = 0
    for row in range(ROWS):
        for col in range(COLS):
            piece = board[row][col]
            if piece != "":
                value = PIECE_VALUES[piece[1]]
                # Add small positional bonus for center control
                if 2 <= row <= 5 and 2 <= col <= 5:
                    value += 0.1

                if piece[0] == 'w':
                    score += value
                else:
                    score -= value
    return score

# Simple AI using basic minimax
def get_ai_move(board):
    best_move = None
    best_score = float('inf')  # AI plays as black, wants lowest score

    moves = get_all_valid_moves(board, 'b')

    # Add some randomness to make AI less predictable
    import random
    random.shuffle(moves)

    for move in moves:
        start, end = move
        # Make the move
        temp_piece = board[end[0]][end[1]]
        board[end[0]][end[1]] = board[start[0]][start[1]]
        board[start[0]][start[1]] = ""

        # Evaluate position
        score = minimax(board, 2, True, float('-inf'), float('inf'))  # Depth 2

        # Undo the move
        board[start[0]][start[1]] = board[end[0]][end[1]]
        board[end[0]][end[1]] = temp_piece

        if score < best_score:
            best_score = score
            best_move = move

    return best_move

# Check if king is in check
def is_in_check(board, color):
    # Find king position
    king_pos = None
    for row in range(ROWS):
        for col in range(COLS):
            piece = board[row][col]
            if piece == f'{color}k':
                king_pos = (row, col)
                break

    if not king_pos:
        return False

    # Check if any enemy piece can attack the king
    enemy_color = 'b' if color == 'w' else 'w'
    for row in range(ROWS):
        for col in range(COLS):
            piece = board[row][col]
            if piece != "" and piece[0] == enemy_color:
                if is_valid_basic_move(board, row, col, king_pos[0], king_pos[1]):
                    return True

    return False

# Check if a move would put own king in check
def would_be_in_check(board, start_row, start_col, end_row, end_col, color):
    # Make the move temporarily
    temp_piece = board[end_row][end_col]
    moving_piece = board[start_row][start_col]
    board[end_row][end_col] = moving_piece
    board[start_row][start_col] = ""

    # Check if king is in check after this move
    in_check = is_in_check(board, color)

    # Undo the move
    board[start_row][start_col] = moving_piece
    board[end_row][end_col] = temp_piece

    return in_check

# Check if player is in checkmate (simplified - only when king is actually captured)
def is_checkmate(board, color):
    # Find the king
    king_pos = None
    for row in range(ROWS):
        for col in range(COLS):
            piece = board[row][col]
            if piece == f'{color}k':
                king_pos = (row, col)
                break

    # Only checkmate if king is actually missing from the board
    if not king_pos:
        return True

    # Otherwise, no checkmate (since we removed check restrictions)
    return False

# Check if player is in stalemate (very conservative - rarely occurs)
def is_stalemate(board, color):
    # Count pieces for both players
    player_pieces = 0
    opponent_pieces = 0

    for row in range(ROWS):
        for col in range(COLS):
            piece = board[row][col]
            if piece != "":
                if piece[0] == color:
                    player_pieces += 1
                else:
                    opponent_pieces += 1

    # Only stalemate if player has very few pieces and no moves
    if player_pieces <= 2:  # Only king + maybe one piece
        # Check if player has any legal moves
        for row in range(ROWS):
            for col in range(COLS):
                piece = board[row][col]
                if piece != "" and piece[0] == color:
                    valid_moves = get_valid_moves(board, row, col)
                    if valid_moves:  # If any piece has valid moves, not stalemate
                        return False
        return True

    return False

# Get game status (simplified - no check warnings)
def get_game_status(board, current_player):
    if is_checkmate(board, current_player):
        winner = "Black" if current_player == 'w' else "White"
        return f"CHECKMATE! {winner} wins!"
    elif is_stalemate(board, current_player):
        return "STALEMATE! It's a draw!"
    else:
        return ""

# Show game over dialog
def show_game_over_dialog(win, message):
    # Create overlay
    overlay = pygame.Surface((WIDTH, HEIGHT))
    overlay.set_alpha(128)
    overlay.fill((0, 0, 0))
    win.blit(overlay, (0, 0))

    # Dialog box
    dialog_width = 450
    dialog_height = 200
    dialog_x = (WIDTH - dialog_width) // 2
    dialog_y = (HEIGHT - dialog_height) // 2

    dialog_rect = pygame.Rect(dialog_x, dialog_y, dialog_width, dialog_height)
    pygame.draw.rect(win, (240, 240, 240), dialog_rect)
    pygame.draw.rect(win, (0, 0, 0), dialog_rect, 3)

    # Fonts
    title_font = pygame.font.Font(None, 36)
    button_font = pygame.font.Font(None, 24)

    # Title text
    title_surface = title_font.render(message, True, (0, 0, 0))
    title_rect = title_surface.get_rect(center=(dialog_x + dialog_width//2, dialog_y + 50))
    win.blit(title_surface, title_rect)

    # Buttons - now three buttons
    button_width = 110
    button_height = 40
    button_y = dialog_y + 120
    button_spacing = 15

    play_again_rect = pygame.Rect(dialog_x + 20, button_y, button_width, button_height)
    menu_rect = pygame.Rect(dialog_x + 20 + button_width + button_spacing, button_y, button_width, button_height)
    exit_rect = pygame.Rect(dialog_x + 20 + 2 * (button_width + button_spacing), button_y, button_width, button_height)

    # Draw buttons
    pygame.draw.rect(win, (100, 200, 100), play_again_rect)  # Green for Play Again
    pygame.draw.rect(win, (100, 149, 237), menu_rect)       # Blue for Back to Menu
    pygame.draw.rect(win, (200, 100, 100), exit_rect)       # Red for Exit
    pygame.draw.rect(win, (0, 0, 0), play_again_rect, 2)
    pygame.draw.rect(win, (0, 0, 0), menu_rect, 2)
    pygame.draw.rect(win, (0, 0, 0), exit_rect, 2)

    # Button text
    play_text = button_font.render("Play Again", True, (0, 0, 0))
    menu_text = button_font.render("Back to Menu", True, (0, 0, 0))
    exit_text = button_font.render("Exit Game", True, (0, 0, 0))

    play_text_rect = play_text.get_rect(center=play_again_rect.center)
    menu_text_rect = menu_text.get_rect(center=menu_rect.center)
    exit_text_rect = exit_text.get_rect(center=exit_rect.center)

    win.blit(play_text, play_text_rect)
    win.blit(menu_text, menu_text_rect)
    win.blit(exit_text, exit_text_rect)

    pygame.display.flip()

    return play_again_rect, menu_rect, exit_rect

# Show exit confirmation dialog
def show_exit_confirmation(win):
    # Create overlay
    overlay = pygame.Surface((WIDTH, HEIGHT))
    overlay.set_alpha(128)
    overlay.fill((0, 0, 0))
    win.blit(overlay, (0, 0))

    # Dialog box
    dialog_width = 400
    dialog_height = 180
    dialog_x = (WIDTH - dialog_width) // 2
    dialog_y = (HEIGHT - dialog_height) // 2

    dialog_rect = pygame.Rect(dialog_x, dialog_y, dialog_width, dialog_height)
    pygame.draw.rect(win, (240, 240, 240), dialog_rect)
    pygame.draw.rect(win, (0, 0, 0), dialog_rect, 3)

    # Fonts
    title_font = pygame.font.Font(None, 28)
    button_font = pygame.font.Font(None, 24)

    # Title text
    title_surface = title_font.render("What would you like to do?", True, (0, 0, 0))
    title_rect = title_surface.get_rect(center=(dialog_x + dialog_width//2, dialog_y + 35))
    win.blit(title_surface, title_rect)

    # Buttons - three options
    button_width = 100
    button_height = 35
    button_y = dialog_y + 80
    button_spacing = 20

    menu_rect = pygame.Rect(dialog_x + 30, button_y, button_width, button_height)
    exit_rect = pygame.Rect(dialog_x + 30 + button_width + button_spacing, button_y, button_width, button_height)
    cancel_rect = pygame.Rect(dialog_x + 30 + 2 * (button_width + button_spacing), button_y, button_width, button_height)

    # Draw buttons
    pygame.draw.rect(win, (100, 149, 237), menu_rect)    # Blue for Back to Menu
    pygame.draw.rect(win, (220, 100, 100), exit_rect)    # Red for Exit Game
    pygame.draw.rect(win, (100, 200, 100), cancel_rect)  # Green for Cancel
    pygame.draw.rect(win, (0, 0, 0), menu_rect, 2)
    pygame.draw.rect(win, (0, 0, 0), exit_rect, 2)
    pygame.draw.rect(win, (0, 0, 0), cancel_rect, 2)

    # Button text
    menu_text = button_font.render("Back to Menu", True, (255, 255, 255))
    exit_text = button_font.render("Exit Game", True, (255, 255, 255))
    cancel_text = button_font.render("Cancel", True, (255, 255, 255))

    menu_text_rect = menu_text.get_rect(center=menu_rect.center)
    exit_text_rect = exit_text.get_rect(center=exit_rect.center)
    cancel_text_rect = cancel_text.get_rect(center=cancel_rect.center)

    win.blit(menu_text, menu_text_rect)
    win.blit(exit_text, exit_text_rect)
    win.blit(cancel_text, cancel_text_rect)

    pygame.display.flip()

    return menu_rect, exit_rect, cancel_rect



# Draw sidebar with game controls and information
def draw_sidebar(win, game_mode, current_player, game_status, move_history, ai_thinking, move_count, chess_timer):
    # Sidebar background
    sidebar_rect = pygame.Rect(BOARD_SIZE, 0, SIDEBAR_WIDTH, HEIGHT)
    pygame.draw.rect(win, SIDEBAR_BG, sidebar_rect)
    pygame.draw.rect(win, BORDER_COLOR, sidebar_rect, 2)

    # Fonts
    title_font = pygame.font.Font(None, 28)
    info_font = pygame.font.Font(None, 24)
    button_font = pygame.font.Font(None, 22)
    timer_font = pygame.font.Font(None, 32)

    y_pos = 20

    # Game title
    title_text = title_font.render("Chess Game", True, TEXT_COLOR)
    win.blit(title_text, (BOARD_SIZE + 10, y_pos))
    y_pos += 40

    # Game mode
    mode_text = "Human vs Human" if game_mode == HUMAN_VS_HUMAN else "Human vs AI"
    mode_surface = info_font.render(f"Mode: {mode_text}", True, TEXT_COLOR)
    win.blit(mode_surface, (BOARD_SIZE + 10, y_pos))
    y_pos += 30

    # Current player
    if ai_thinking:
        player_text = "AI is thinking..."
        color = WARNING_COLOR
    else:
        player_name = "White" if current_player == 'w' else "Black"
        if game_mode == HUMAN_VS_AI and current_player == 'b':
            player_name += " (AI)"
        player_text = f"Turn: {player_name}"
        color = TEXT_COLOR

    player_surface = info_font.render(player_text, True, color)
    win.blit(player_surface, (BOARD_SIZE + 10, y_pos))
    y_pos += 30

    # Move counter
    move_text = f"Moves: {move_count}"
    move_surface = info_font.render(move_text, True, TEXT_COLOR)
    win.blit(move_surface, (BOARD_SIZE + 10, y_pos))
    y_pos += 40

    # Chess timers
    timer_title = info_font.render("Game Timers:", True, TEXT_COLOR)
    win.blit(timer_title, (BOARD_SIZE + 10, y_pos))
    y_pos += 25

    # White timer
    white_time_str = chess_timer.get_time_string('w')
    white_color = chess_timer.get_time_color('w')
    white_active = current_player == 'w' and chess_timer.active

    white_label = info_font.render("White:", True, TEXT_COLOR)
    white_time = timer_font.render(white_time_str, True, white_color)

    win.blit(white_label, (BOARD_SIZE + 10, y_pos))
    win.blit(white_time, (BOARD_SIZE + 70, y_pos - 3))

    # Draw active indicator
    if white_active:
        pygame.draw.circle(win, SUCCESS_COLOR, (BOARD_SIZE + 170, y_pos + 10), 5)

    y_pos += 30

    # Black timer
    black_time_str = chess_timer.get_time_string('b')
    black_color = chess_timer.get_time_color('b')
    black_active = current_player == 'b' and chess_timer.active

    black_label = info_font.render("Black:", True, TEXT_COLOR)
    black_time = timer_font.render(black_time_str, True, black_color)

    win.blit(black_label, (BOARD_SIZE + 10, y_pos))
    win.blit(black_time, (BOARD_SIZE + 70, y_pos - 3))

    # Draw active indicator
    if black_active:
        pygame.draw.circle(win, SUCCESS_COLOR, (BOARD_SIZE + 170, y_pos + 10), 5)

    y_pos += 40

    # Game status
    if game_status:
        if "CHECKMATE" in game_status:
            status_color = ERROR_COLOR
        elif "STALEMATE" in game_status:
            status_color = WARNING_COLOR
        elif "TIME UP" in game_status:
            status_color = ERROR_COLOR
        else:
            status_color = TEXT_COLOR

        # Wrap long status text
        words = game_status.split()
        lines = []
        current_line = ""
        for word in words:
            test_line = current_line + " " + word if current_line else word
            if info_font.size(test_line)[0] < SIDEBAR_WIDTH - 20:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        if current_line:
            lines.append(current_line)

        for line in lines:
            status_surface = info_font.render(line, True, status_color)
            win.blit(status_surface, (BOARD_SIZE + 10, y_pos))
            y_pos += 25
        y_pos += 20

    # Control buttons
    button_width = SIDEBAR_WIDTH - 20
    button_height = 35
    button_spacing = 10

    buttons = []

    # Undo button
    undo_rect = pygame.Rect(BOARD_SIZE + 10, y_pos, button_width, button_height)
    undo_enabled = move_history.can_undo() and not ai_thinking
    undo_color = BUTTON_COLOR if undo_enabled else BUTTON_DISABLED
    pygame.draw.rect(win, undo_color, undo_rect)
    pygame.draw.rect(win, BORDER_COLOR, undo_rect, 2)

    undo_text = button_font.render("Undo Move", True, TEXT_COLOR)
    undo_text_rect = undo_text.get_rect(center=undo_rect.center)
    win.blit(undo_text, undo_text_rect)
    buttons.append(('undo', undo_rect, undo_enabled))
    y_pos += button_height + button_spacing

    # Redo button
    redo_rect = pygame.Rect(BOARD_SIZE + 10, y_pos, button_width, button_height)
    redo_enabled = move_history.can_redo() and not ai_thinking
    redo_color = BUTTON_COLOR if redo_enabled else BUTTON_DISABLED
    pygame.draw.rect(win, redo_color, redo_rect)
    pygame.draw.rect(win, BORDER_COLOR, redo_rect, 2)

    redo_text = button_font.render("Redo Move", True, TEXT_COLOR)
    redo_text_rect = redo_text.get_rect(center=redo_rect.center)
    win.blit(redo_text, redo_text_rect)
    buttons.append(('redo', redo_rect, redo_enabled))
    y_pos += button_height + button_spacing

    # New Game button
    new_game_rect = pygame.Rect(BOARD_SIZE + 10, y_pos, button_width, button_height)
    pygame.draw.rect(win, SUCCESS_COLOR, new_game_rect)
    pygame.draw.rect(win, BORDER_COLOR, new_game_rect, 2)

    new_game_text = button_font.render("New Game", True, PIECE_WHITE)
    new_game_text_rect = new_game_text.get_rect(center=new_game_rect.center)
    win.blit(new_game_text, new_game_text_rect)
    buttons.append(('new_game', new_game_rect, True))
    y_pos += button_height + button_spacing

    # Back to Menu button
    menu_rect = pygame.Rect(BOARD_SIZE + 10, y_pos, button_width, button_height)
    pygame.draw.rect(win, BUTTON_COLOR, menu_rect)
    pygame.draw.rect(win, BORDER_COLOR, menu_rect, 2)

    menu_text = button_font.render("Back to Menu", True, PIECE_WHITE)
    menu_text_rect = menu_text.get_rect(center=menu_rect.center)
    win.blit(menu_text, menu_text_rect)
    buttons.append(('menu', menu_rect, True))
    y_pos += button_height + button_spacing

    # Exit button
    exit_rect = pygame.Rect(BOARD_SIZE + 10, y_pos, button_width, button_height)
    pygame.draw.rect(win, ERROR_COLOR, exit_rect)
    pygame.draw.rect(win, BORDER_COLOR, exit_rect, 2)

    exit_text = button_font.render("Exit Game", True, PIECE_WHITE)
    exit_text_rect = exit_text.get_rect(center=exit_rect.center)
    win.blit(exit_text, exit_text_rect)
    buttons.append(('exit', exit_rect, True))

    return buttons

# Simple minimax algorithm with alpha-beta pruning
def minimax(board, depth, maximizing_player, alpha, beta):
    if depth == 0:
        return evaluate_board(board)

    if maximizing_player:  # White's turn
        max_eval = float('-inf')
        moves = get_all_valid_moves(board, 'w')
        for move in moves:
            start, end = move
            temp_piece = board[end[0]][end[1]]
            board[end[0]][end[1]] = board[start[0]][start[1]]
            board[start[0]][start[1]] = ""

            eval_score = minimax(board, depth - 1, False, alpha, beta)

            board[start[0]][start[1]] = board[end[0]][end[1]]
            board[end[0]][end[1]] = temp_piece

            max_eval = max(max_eval, eval_score)
            alpha = max(alpha, eval_score)
            if beta <= alpha:
                break
        return max_eval
    else:  # Black's turn
        min_eval = float('inf')
        moves = get_all_valid_moves(board, 'b')
        for move in moves:
            start, end = move
            temp_piece = board[end[0]][end[1]]
            board[end[0]][end[1]] = board[start[0]][start[1]]
            board[start[0]][start[1]] = ""

            eval_score = minimax(board, depth - 1, True, alpha, beta)

            board[start[0]][start[1]] = board[end[0]][end[1]]
            board[end[0]][end[1]] = temp_piece

            min_eval = min(min_eval, eval_score)
            beta = min(beta, eval_score)
            if beta <= alpha:
                break
        return min_eval

# Draw menu screen
def draw_menu(win):
    win.fill(MENU_BG)
    font_title = pygame.font.Font(None, 72)
    font_button = pygame.font.Font(None, 48)

    # Title
    title = font_title.render("Enhanced Chess Game", True, TEXT_COLOR)
    # subtitle = pygame.font.Font(None, 32).render("with Undo/Redo & Advanced UI", True, TEXT_COLOR)
    title_rect = title.get_rect(center=(WIDTH // 2, 80))
    # subtitle_rect = subtitle.get_rect(center=(WIDTH // 2, 110))
    win.blit(title, title_rect)
    # win.blit(subtitle, subtitle_rect)

    # Buttons
    button1_rect = pygame.Rect(WIDTH // 2 - 150, 200, 300, 60)
    button2_rect = pygame.Rect(WIDTH // 2 - 150, 300, 300, 60)

    pygame.draw.rect(win, BUTTON_COLOR, button1_rect)
    pygame.draw.rect(win, BUTTON_COLOR, button2_rect)
    pygame.draw.rect(win, TEXT_COLOR, button1_rect, 3)
    pygame.draw.rect(win, TEXT_COLOR, button2_rect, 3)

    # Button text
    text1 = font_button.render("Human vs Human", True, TEXT_COLOR)
    text2 = font_button.render("Human vs AI", True, TEXT_COLOR)

    text1_rect = text1.get_rect(center=button1_rect.center)
    text2_rect = text2.get_rect(center=button2_rect.center)

    win.blit(text1, text1_rect)
    win.blit(text2, text2_rect)

    return button1_rect, button2_rect

# Show menu and get game mode
def show_menu():
    pygame.init()
    win = pygame.display.set_mode((WIDTH, HEIGHT))
    pygame.display.set_caption("Chess Game - Select Mode")
    clock = pygame.time.Clock()

    while True:
        button1_rect, button2_rect = draw_menu(win)
        pygame.display.flip()

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return None  # Signal to exit completely

            if event.type == pygame.MOUSEBUTTONDOWN:
                mouse_pos = pygame.mouse.get_pos()
                if button1_rect.collidepoint(mouse_pos):
                    return HUMAN_VS_HUMAN
                elif button2_rect.collidepoint(mouse_pos):
                    return HUMAN_VS_AI

        clock.tick(60)

# Game loop function (separated from main for restart functionality)
def run_game(game_mode):
    pygame.init()
    win = pygame.display.set_mode((WIDTH, HEIGHT))
    pygame.display.set_caption("Chess Game (Group 3)")
    clock = pygame.time.Clock()

    # Load piece images
    load_piece_images()

    board = create_board()
    selected = None
    valid_moves = []
    current_player = 'w'  # White starts
    running = True
    ai_thinking = False
    ai_move_time = 0  # Timer for AI delay
    game_over = False
    game_status = ""
    show_dialog = False
    show_exit_dialog = False
    move_history = MoveHistory()
    move_count = 0
    chess_timer = ChessTimer(30)  # 30 minutes per player
    chess_timer.start_timer('w')  # Start with white player



    while running:
        clock.tick(60)

        # Update chess timer
        if not game_over and not show_dialog and not show_exit_dialog:
            chess_timer.update_time()

            # Check if current player's time is up
            if chess_timer.is_time_up(current_player):
                winner = "Black" if current_player == 'w' else "White"
                game_status = f"TIME UP! {winner} wins!"
                game_over = True
                show_dialog = True
                chess_timer.pause_timer()

        # Check for game over conditions
        if not game_over:
            game_status = get_game_status(board, current_player)
            if "CHECKMATE" in game_status or "STALEMATE" in game_status:
                game_over = True
                show_dialog = True
                chess_timer.pause_timer()

        # AI move (if it's AI's turn and game not over)
        if not game_over and game_mode == HUMAN_VS_AI and current_player == 'b':
            if not ai_thinking:
                # Start AI thinking process
                ai_thinking = True
                ai_move_time = pygame.time.get_ticks()  # Record start time
            else:
                # Check if enough time has passed (1 second delay)
                current_time = pygame.time.get_ticks()
                if current_time - ai_move_time >= 1000:  # 1000ms = 1 second
                    ai_move = get_ai_move(board)
                    if ai_move:
                        start, end = ai_move

                        # Save AI move for undo/redo
                        move_data = {
                            'board_before': [row[:] for row in board],
                            'board_after': None,
                            'player_before': current_player,
                            'player_after': None,
                            'from_pos': start,
                            'to_pos': end,
                            'captured_piece': board[end[0]][end[1]]
                        }

                        # Execute AI move
                        board[end[0]][end[1]] = board[start[0]][start[1]]
                        board[start[0]][start[1]] = ""
                        current_player = 'w'
                        move_count += 1

                        # Switch chess timer
                        chess_timer.switch_player(current_player)

                        # Complete move data and save
                        move_data['board_after'] = [row[:] for row in board]
                        move_data['player_after'] = current_player
                        move_history.add_move(move_data)

                        selected = None
                        valid_moves = []
                    ai_thinking = False

        # Clear screen
        win.fill((250, 250, 250))

        # Draw board
        draw_board(win)

        # Highlight valid moves
        for move_row, move_col in valid_moves:
            pygame.draw.rect(win, VALID_MOVE,
                           (move_col*SQUARE_SIZE, move_row*SQUARE_SIZE, SQUARE_SIZE, SQUARE_SIZE), 3)

        # Highlight selected piece
        if selected:
            row, col = selected
            pygame.draw.rect(win, HIGHLIGHT,
                           (col*SQUARE_SIZE, row*SQUARE_SIZE, SQUARE_SIZE, SQUARE_SIZE), 4)

        # Draw pieces
        draw_pieces(win, board)

        # Draw sidebar with controls
        sidebar_buttons = draw_sidebar(win, game_mode, current_player, game_status,
                                     move_history, ai_thinking, move_count, chess_timer)

        # Show dialogs
        if show_dialog:
            play_again_rect, menu_rect, exit_rect = show_game_over_dialog(win, game_status)
            menu_exit_rect = None
            exit_exit_rect = None
            cancel_exit_rect = None
        elif show_exit_dialog:
            menu_exit_rect, exit_exit_rect, cancel_exit_rect = show_exit_confirmation(win)
            play_again_rect = None
            menu_rect = None
            exit_rect = None
        else:
            pygame.display.flip()
            play_again_rect = None
            menu_rect = None
            exit_rect = None
            menu_exit_rect = None
            exit_exit_rect = None
            cancel_exit_rect = None

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                # Show exit confirmation instead of immediately quitting
                if not show_exit_dialog and not show_dialog:
                    show_exit_dialog = True
                else:
                    running = False

            # Handle dialog button clicks
            if event.type == pygame.MOUSEBUTTONDOWN and show_dialog:
                mouse_pos = pygame.mouse.get_pos()
                if play_again_rect and play_again_rect.collidepoint(mouse_pos):
                    # Restart game
                    board = create_board()
                    selected = None
                    valid_moves = []
                    current_player = 'w'
                    game_over = False
                    game_status = ""
                    show_dialog = False
                    ai_thinking = False
                    move_history.clear()
                    move_count = 0
                    # Reset and start timer
                    chess_timer.reset(30)
                    chess_timer.start_timer('w')
                elif menu_rect and menu_rect.collidepoint(mouse_pos):
                    # Return to menu
                    return "menu"
                elif exit_rect and exit_rect.collidepoint(mouse_pos):
                    # Exit game completely
                    return "exit"

            # Handle exit confirmation dialog
            if event.type == pygame.MOUSEBUTTONDOWN and show_exit_dialog:
                mouse_pos = pygame.mouse.get_pos()
                if menu_exit_rect and menu_exit_rect.collidepoint(mouse_pos):
                    # Return to menu
                    return "menu"
                elif exit_exit_rect and exit_exit_rect.collidepoint(mouse_pos):
                    # Exit game completely
                    return "exit"
                elif cancel_exit_rect and cancel_exit_rect.collidepoint(mouse_pos):
                    # User cancelled exit
                    show_exit_dialog = False



            # Handle sidebar button clicks
            if event.type == pygame.MOUSEBUTTONDOWN and not show_dialog and not show_exit_dialog:
                mouse_pos = pygame.mouse.get_pos()
                for button_type, button_rect, enabled in sidebar_buttons:
                    if button_rect.collidepoint(mouse_pos) and enabled:
                        if button_type == 'undo':
                            # Undo last move
                            move_data = move_history.undo()
                            if move_data:
                                board = [row[:] for row in move_data['board_before']]
                                current_player = move_data['player_before']
                                selected = None
                                valid_moves = []
                                move_count -= 1
                                game_over = False
                                game_status = ""
                                # Switch timer back to previous player
                                chess_timer.switch_player(current_player)

                        elif button_type == 'redo':
                            # Redo next move
                            move_data = move_history.redo()
                            if move_data:
                                board = [row[:] for row in move_data['board_after']]
                                current_player = move_data['player_after']
                                selected = None
                                valid_moves = []
                                move_count += 1
                                # Switch timer to current player
                                chess_timer.switch_player(current_player)

                        elif button_type == 'new_game':
                            # Start new game
                            board = create_board()
                            selected = None
                            valid_moves = []
                            current_player = 'w'
                            game_over = False
                            game_status = ""
                            ai_thinking = False
                            move_history.clear()
                            move_count = 0
                            # Reset and start timer
                            chess_timer.reset(30)
                            chess_timer.start_timer('w')

                        elif button_type == 'menu':
                            # Return to menu
                            return "menu"

                        elif button_type == 'exit':
                            # Show exit confirmation
                            show_exit_dialog = True

            if event.type == pygame.MOUSEBUTTONDOWN and not ai_thinking and not game_over and not show_dialog and not show_exit_dialog:
                x, y = pygame.mouse.get_pos()
                row, col = y // SQUARE_SIZE, x // SQUARE_SIZE

                # Make sure click is within board
                if 0 <= row < ROWS and 0 <= col < COLS:
                    # Only allow human moves
                    if game_mode == HUMAN_VS_HUMAN or (game_mode == HUMAN_VS_AI and current_player == 'w'):
                        if selected:
                            # Try to make a move
                            if (row, col) in valid_moves:
                                # Save move for undo/redo
                                move_data = {
                                    'board_before': [row[:] for row in board],
                                    'board_after': None,
                                    'player_before': current_player,
                                    'player_after': None,
                                    'from_pos': selected,
                                    'to_pos': (row, col),
                                    'captured_piece': board[row][col]
                                }

                                # Execute move
                                piece = board[selected[0]][selected[1]]
                                board[selected[0]][selected[1]] = ""
                                board[row][col] = piece

                                # Switch players
                                current_player = 'b' if current_player == 'w' else 'w'
                                move_count += 1

                                # Switch chess timer
                                chess_timer.switch_player(current_player)

                                # Complete move data and save
                                move_data['board_after'] = [row[:] for row in board]
                                move_data['player_after'] = current_player
                                move_history.add_move(move_data)

                                selected = None
                                valid_moves = []
                            else:
                                # Invalid move or selecting new piece
                                piece = board[row][col]
                                if piece != "" and piece[0] == current_player:
                                    selected = (row, col)
                                    valid_moves = get_valid_moves(board, row, col)
                                else:
                                    selected = None
                                    valid_moves = []
                        else:
                            # Select a piece
                            piece = board[row][col]
                            if piece != "" and piece[0] == current_player:
                                selected = (row, col)
                                valid_moves = get_valid_moves(board, row, col)



    # If we reach here, the game loop ended normally (window closed)
    return "exit"

# Main function that handles the overall game flow
def main():
    """
    Main game function that handles the menu loop and game restarts.
    Allows players to return to the menu after games or exit completely.
    """
    pygame.init()

    while True:
        # Show menu and get game mode selection
        game_mode = show_menu()

        # If user closed the menu window, exit completely
        if game_mode is None:
            break

        # Run the game with selected mode
        result = run_game(game_mode)

        # Handle the result
        if result == "exit":
            # User chose to exit completely
            break
        elif result == "menu":
            # User chose to return to menu, continue the loop
            continue
        else:
            # Unexpected result, exit safely
            break

    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()
