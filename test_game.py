# #!/usr/bin/env python3
# """
# Test script to verify the chess game functionality
# """

# import pygame
# import sys
# import os

# def test_imports():
#     """Test that all required modules can be imported"""
#     try:
#         import chess
#         # Test that we can access a function from the module
#         hasattr(chess, 'create_board')
#         print("✓ Chess module imported successfully")
#         return True
#     except ImportError as e:
#         print(f"✗ Failed to import chess module: {e}")
#         return False

# def test_pygame():
#     """Test that pygame is working"""
#     try:
#         pygame.init()
#         print("✓ Pygame initialized successfully")
#         pygame.quit()
#         return True
#     except Exception as e:
#         print(f"✗ Pygame initialization failed: {e}")
#         return False

# def test_piece_images():
#     """Test that piece images exist"""
#     pieces = ['wp', 'wr', 'wn', 'wb', 'wq', 'wk',
#               'bp', 'br', 'bn', 'bb', 'bq', 'bk']

#     missing_pieces = []
#     for piece in pieces:
#         if not os.path.exists(f"pieces/{piece}.png"):
#             missing_pieces.append(piece)

#     if missing_pieces:
#         print(f"✗ Missing piece images: {', '.join(missing_pieces)}")
#         print("  Run 'python create_piece_images.py' to create them")
#         return False
#     else:
#         print("✓ All piece images found")
#         return True

# def test_board_creation():
#     """Test board creation and basic functions"""
#     try:
#         # Import the chess module functions
#         sys.path.append('.')
#         from chess import create_board, is_valid_move
        
#         board = create_board()
        
#         # Test board structure
#         if len(board) != 8 or len(board[0]) != 8:
#             print("✗ Board has incorrect dimensions")
#             return False

#         # Test that pieces are in starting positions
#         if board[0][0] != "br" or board[7][4] != "wk":
#             print("✗ Pieces not in correct starting positions")
#             return False

#         # Test a simple valid move (white pawn forward 2)
#         if not is_valid_move(board, 6, 4, 4, 4):
#             print("✗ Valid pawn move not recognized")
#             return False

#         # Test invalid move (pawn forward 3 - invalid)
#         if is_valid_move(board, 6, 4, 3, 4):
#             print("✗ Invalid pawn move was accepted")
#             return False
        
#         print("✓ Board creation and move validation working")
#         return True
        
#     except Exception as e:
#         print(f"✗ Board testing failed: {e}")
#         return False

# def main():
#     """Run all tests"""
#     print("Testing Chess Game Components...")
#     print("=" * 40)
    
#     tests = [
#         test_pygame,
#         test_piece_images,
#         test_board_creation,
#     ]
    
#     passed = 0
#     total = len(tests)
    
#     for test in tests:
#         if test():
#             passed += 1
#         print()
    
#     print("=" * 40)
#     print(f"Tests passed: {passed}/{total}")
    
#     if passed == total:
#         print("🎉 All tests passed! The game should work correctly.")
#         print("Run 'python chess.py' to start playing!")
#     else:
#         print("⚠️  Some tests failed. Please check the issues above.")
    
#     return passed == total

# if __name__ == "__main__":
#     success = main()
#     sys.exit(0 if success else 1)
