# Enhanced 2D Chess Game

A fully functional 2D chess game built with Python and Pygame featuring advanced UI, undo/redo functionality, and AI opponent support.

## ✨ Features

### 🎮 **Game Modes**
- **Human vs Human**: Two players on the same computer
- **Human vs AI**: Play against intelligent computer opponent with 1-second thinking delay

### 🎨 **Enhanced User Interface**
- **Modern Sidebar Design**: Clean, organized control panel
- **Board Coordinates**: File (a-h) and rank (1-8) labels for easy reference
- **Color-Coded Status**: Visual feedback with different colors for different game states
- **Move Counter**: Track the number of moves played
- **Real-time Game Information**: Current player, game mode, and status display

### 🔄 **Undo/Redo System**
- **Undo Move**: Take back your last move (works for both human and AI moves)
- **Redo Move**: Replay undone moves
- **Complete Move History**: Full tracking of all game moves
- **Smart Button States**: Buttons automatically enable/disable based on availability

### 🎯 **Game Controls**
- **New Game Button**: Start fresh game anytime
- **Exit Game Button**: Safe exit with confirmation dialog
- **Exit Confirmation**: "Do you want to exit the game? Yes/No" dialog prevents accidental exits
- **Visual Button Feedback**: Hover effects and disabled states
- **Mouse-Only Interface**: No keyboard required for gameplay

### 🏆 **Complete Chess Implementation**
- **All Piece Movements**: Proper rules for pawns, rooks, bishops, knights, queens, and kings
- **Free Movement System**: Players can move pieces freely without check restrictions
- **Simple Pawn Rules**: Pawns stay as pawns throughout the game (no promotion)
- **30-Minute Timer System**: Each player gets 30 minutes total game time
- **Real-time Timer Display**: Color-coded timers (green/orange/red based on remaining time)
- **Time-based Win Conditions**: Game ends when a player runs out of time
- **Win/Lose Detection**:
  - **Checkmate**: Game ends when king is captured
  - **Stalemate**: Draw detection when no legal moves available
  - **Time Up**: Player loses when their timer reaches 00:00
- **Beautiful Piece Graphics**: Custom-drawn piece icons with PNG image support
- **Valid Move Highlighting**: Green squares show all legal moves for selected pieces

### ⏱️ **Chess Timer System**
- **30-Minute Games**: Each player starts with 30:00 on their timer
- **Active Player Timing**: Timer counts down only for the current player
- **Visual Timer Display**: Large, easy-to-read timer format (MM:SS)
- **Color-Coded Warnings**:
  - 🟢 Green: More than 1 minute remaining
  - 🟠 Orange: 30-60 seconds remaining
  - 🔴 Red: Less than 30 seconds remaining
- **Active Player Indicator**: Green dot shows whose timer is running
- **Time-Up Detection**: Game ends automatically when timer reaches 00:00

### 👑 **Pawn Promotion System**
- **Automatic Detection**: When pawn reaches opposite end (row 0 for white, row 7 for black)
- **Interactive Choice Dialog**: Beautiful selection interface with piece symbols
- **Four Promotion Options**:
  - ♛ **Queen**: Most powerful piece (recommended)
  - ♜ **Rook**: Strong for endgame control
  - ♝ **Bishop**: Good for diagonal attacks
  - ♞ **Knight**: Unique L-shaped movement
- **Timer Pause**: Game timer pauses during promotion selection
- **AI Auto-Promotion**: AI automatically promotes to Queen for optimal play

### 💬 **Smart Dialogs**
- **Game Over Dialog**: When checkmate/stalemate/time-up occurs, shows "Play Again" or "Exit" options
- **Exit Confirmation**: Prevents accidental game closure with "Yes/No" confirmation
- **Pawn Promotion Dialog**: Interactive piece selection when pawn reaches end
- **Modal Interface**: Dialogs pause gameplay until user makes a choice
- **Visual Feedback**: Color-coded buttons (green for continue, red for exit)

## How to Play

1. **Run the game**: `python chess.py`
2. **Choose game mode**: Select "Human vs Human" or "Human vs AI" from the menu
3. **Select a piece**: Click on any piece of the current player's color
4. **View valid moves**: Green squares show where the selected piece can move
5. **Make a move**: Click on any highlighted green square to move there
6. **AI turns**: In AI mode, the computer will automatically make moves for Black pieces

## 🎮 Controls & Interface

### **Main Game Controls**
- **Left Click**: Select pieces and make moves on the chess board
- **Sidebar Buttons**: Click buttons in the right panel for game controls

### **Sidebar Functions**
- **Undo Move**: Take back the last move (human or AI)
- **Redo Move**: Replay a previously undone move
- **New Game**: Start a fresh game with reset board
- **Exit Game**: Close the application

### **Visual Feedback**
- **Green Squares**: Show valid moves for selected piece
- **Yellow Highlight**: Currently selected piece
- **Color-Coded Status**:
  - 🟢 Green for normal gameplay
  - 🟡 Yellow for check warnings
  - 🔴 Red for checkmate
  - 🟠 Orange for stalemate

## Game Modes

### Human vs Human
- Two players take turns on the same computer
- White player goes first, then alternates with Black

### Human vs AI
- Human player controls White pieces
- AI controls Black pieces using intelligent minimax algorithm
- AI evaluates positions and makes strategic moves
- **1-second delay** after human moves for natural game flow

## Requirements

- Python 3.x
- Pygame library (`pip install pygame`)

## Installation & Setup

1. Clone or download the game files
2. Install Pygame: `pip install pygame`
3. Run the piece image generator: `python create_piece_images.py`
4. Start the game: `python chess.py`

## Game Rules Implemented

- **Pawns**: Move forward one square, two squares on first move, capture diagonally
- **Rooks**: Move horizontally or vertically any distance
- **Bishops**: Move diagonally any distance
- **Knights**: Move in L-shape (2+1 squares)
- **Queens**: Combine rook and bishop movement
- **Kings**: Move one square in any direction

## AI Features

- **Minimax Algorithm**: AI uses minimax with alpha-beta pruning for efficient move calculation
- **Position Evaluation**: AI evaluates board positions based on piece values and positioning
- **Strategic Play**: AI considers captures, threats, and piece development
- **Natural Timing**: 1-second delay after human moves for realistic game pacing
- **Adjustable Difficulty**: AI depth can be modified for stronger/weaker play

## Files Included

- `chess.py` - Main game file with all chess logic and AI
- `create_piece_images.py` - Utility to generate piece image files
- `pieces/` - Directory containing piece PNG images
- `README.md` - This documentation file

## Game End Conditions

- **Checkmate**: When a king is in check and has no legal moves to escape
  - Game displays "CHECKMATE! [Winner] wins!" in red
  - No further moves are allowed
- **Stalemate**: When a player has no legal moves but is not in check
  - Game displays "STALEMATE! It's a draw!" in orange
  - Results in a tie game
- **Check**: When a king is under attack but can still move
  - Game displays "CHECK! [Color] king is in danger!" in yellow
  - Player must make a move to get out of check

## Future Enhancements

- Castling and en passant moves
- Pawn promotion
- Game state saving/loading
- Multiple AI difficulty levels
- Move history and undo functionality
- Online multiplayer support
