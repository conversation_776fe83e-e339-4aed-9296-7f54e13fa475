"""
Simple Clean Chess Game
Minimal implementation with essential features only.
"""

import pygame
import sys
import random
from typing import List, Tuple, Optional

# Constants
BOARD_SIZE = 640
SIDEBAR_WIDTH = 200
WIDTH, HEIGHT = BOARD_SIZE + SIDEBAR_WIDTH, <PERSON>OARD_SIZE + 50
ROWS, COLS = 8, 8
SQUARE_SIZE = BOARD_SIZE // COLS

# Colors
WHITE = (240, 217, 181)
BLACK = (181, 136, 99)
HIGHLIGHT = (186, 202, 68)
VALID_MOVE = (144, 238, 144)
TEXT_COLOR = (50, 50, 50)
BUTTON_COLOR = (70, 130, 180)
SIDEBAR_BG = (245, 245, 245)
SUCCESS_COLOR = (34, 139, 34)
ERROR_COLOR = (220, 20, 60)

# Game modes
HUMAN_VS_HUMAN = 1
HUMAN_VS_AI = 2

# Piece values for AI
PIECE_VALUES = {'p': 1, 'n': 3, 'b': 3, 'r': 5, 'q': 9, 'k': 100}

class ChessGame:
    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode((WIDTH, HEIGHT))
        pygame.display.set_caption("Simple Chess Game")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 24)
        self.piece_images = {}
        self.load_piece_images()
        self.reset_game()

    def load_piece_images(self):
        """Load chess piece images."""
        pieces = ['wp', 'wr', 'wn', 'wb', 'wq', 'wk', 'bp', 'br', 'bn', 'bb', 'bq', 'bk']
        for piece in pieces:
            try:
                image = pygame.image.load(f"pieces/{piece}.png")
                self.piece_images[piece] = pygame.transform.scale(image, (SQUARE_SIZE, SQUARE_SIZE))
            except pygame.error:
                self.piece_images[piece] = None
    
    def reset_game(self):
        """Reset game to initial state."""
        self.board = self.create_board()
        self.current_player = 'w'
        self.selected = None
        self.valid_moves = []
        self.game_over = False
        self.move_count = 0
    
    def create_board(self) -> List[List[str]]:
        """Create initial chess board."""
        return [
            ["br", "bn", "bb", "bq", "bk", "bb", "bn", "br"],  # Row 0: Black pieces
            ["bp"] * 8,                                         # Row 1: Black pawns
            [""] * 8,                                           # Row 2: Empty
            [""] * 8,                                           # Row 3: Empty
            [""] * 8,                                           # Row 4: Empty
            [""] * 8,                                           # Row 5: Empty
            ["wp"] * 8,                                         # Row 6: White pawns
            ["wr", "wn", "wb", "wq", "wk", "wb", "wn", "wr"]   # Row 7: White pieces
        ]
    
    def is_valid_move(self, from_pos: Tuple[int, int], to_pos: Tuple[int, int]) -> bool:
        """Check if move is valid."""
        from_row, from_col = from_pos
        to_row, to_col = to_pos
        
        if not (0 <= to_row < ROWS and 0 <= to_col < COLS):
            return False
        
        piece = self.board[from_row][from_col]
        target = self.board[to_row][to_col]
        
        if not piece or (target and target[0] == piece[0]):
            return False
        
        piece_type = piece[1]
        row_diff = to_row - from_row
        col_diff = to_col - from_col
        
        # Simple piece movement rules
        if piece_type == 'p':  # Pawn
            direction = -1 if piece[0] == 'w' else 1
            start_row = 6 if piece[0] == 'w' else 1  # Starting row for pawns

            # Forward move (1 square)
            if col_diff == 0 and row_diff == direction and not target:
                return True
            # Initial 2-square move
            if col_diff == 0 and row_diff == 2 * direction and from_row == start_row and not target:
                return True
            # Diagonal capture
            if abs(col_diff) == 1 and row_diff == direction and target:
                return True
        elif piece_type == 'r':  # Rook
            return (row_diff == 0 or col_diff == 0) and self.is_path_clear(from_pos, to_pos)
        elif piece_type == 'b':  # Bishop
            return abs(row_diff) == abs(col_diff) and self.is_path_clear(from_pos, to_pos)
        elif piece_type == 'q':  # Queen
            return (row_diff == 0 or col_diff == 0 or abs(row_diff) == abs(col_diff)) and self.is_path_clear(from_pos, to_pos)
        elif piece_type == 'k':  # King
            return abs(row_diff) <= 1 and abs(col_diff) <= 1
        elif piece_type == 'n':  # Knight
            return (abs(row_diff) == 2 and abs(col_diff) == 1) or (abs(row_diff) == 1 and abs(col_diff) == 2)
        
        return False
    
    def is_path_clear(self, from_pos: Tuple[int, int], to_pos: Tuple[int, int]) -> bool:
        """Check if path is clear for sliding pieces."""
        from_row, from_col = from_pos
        to_row, to_col = to_pos
        
        row_step = 0 if from_row == to_row else (1 if to_row > from_row else -1)
        col_step = 0 if from_col == to_col else (1 if to_col > from_col else -1)
        
        current_row, current_col = from_row + row_step, from_col + col_step
        
        while current_row != to_row or current_col != to_col:
            if self.board[current_row][current_col]:
                return False
            current_row += row_step
            current_col += col_step
        
        return True
    
    def get_valid_moves(self, from_pos: Tuple[int, int]) -> List[Tuple[int, int]]:
        """Get all valid moves for a piece."""
        moves = []
        for row in range(ROWS):
            for col in range(COLS):
                if self.is_valid_move(from_pos, (row, col)):
                    moves.append((row, col))
        return moves
    
    def make_move(self, from_pos: Tuple[int, int], to_pos: Tuple[int, int]):
        """Make a move on the board."""
        from_row, from_col = from_pos
        to_row, to_col = to_pos
        
        piece = self.board[from_row][from_col]
        self.board[to_row][to_col] = piece
        self.board[from_row][from_col] = ""
        
        self.current_player = 'b' if self.current_player == 'w' else 'w'
        self.move_count += 1
        self.selected = None
        self.valid_moves = []
    
    def get_ai_move(self) -> Optional[Tuple[Tuple[int, int], Tuple[int, int]]]:
        """Get AI move using simple evaluation."""
        moves = []
        for row in range(ROWS):
            for col in range(COLS):
                piece = self.board[row][col]
                if piece and piece[0] == 'b':
                    valid_moves = self.get_valid_moves((row, col))
                    for to_pos in valid_moves:
                        moves.append(((row, col), to_pos))
        
        if not moves:
            return None
        
        # Simple AI: prefer captures, then random
        capture_moves = []
        for from_pos, to_pos in moves:
            if self.board[to_pos[0]][to_pos[1]]:
                capture_moves.append((from_pos, to_pos))
        
        if capture_moves:
            return random.choice(capture_moves)
        return random.choice(moves)
    
    def is_game_over(self) -> bool:
        """Check if game is over (simplified)."""
        # Count kings
        white_king = black_king = False
        for row in range(ROWS):
            for col in range(COLS):
                piece = self.board[row][col]
                if piece == 'wk':
                    white_king = True
                elif piece == 'bk':
                    black_king = True
        
        return not (white_king and black_king)
    
    def draw_board(self):
        """Draw the chess board."""
        for row in range(ROWS):
            for col in range(COLS):
                color = WHITE if (row + col) % 2 == 0 else BLACK
                rect = pygame.Rect(col * SQUARE_SIZE, row * SQUARE_SIZE, SQUARE_SIZE, SQUARE_SIZE)
                pygame.draw.rect(self.screen, color, rect)
        
        # Draw valid moves
        for row, col in self.valid_moves:
            rect = pygame.Rect(col * SQUARE_SIZE, row * SQUARE_SIZE, SQUARE_SIZE, SQUARE_SIZE)
            pygame.draw.rect(self.screen, VALID_MOVE, rect, 3)
        
        # Draw selected piece
        if self.selected:
            row, col = self.selected
            rect = pygame.Rect(col * SQUARE_SIZE, row * SQUARE_SIZE, SQUARE_SIZE, SQUARE_SIZE)
            pygame.draw.rect(self.screen, HIGHLIGHT, rect, 4)
        
        # Draw pieces (images with fallback to symbols)
        piece_symbols = {
            'wp': '♙', 'wr': '♖', 'wn': '♘', 'wb': '♗', 'wq': '♕', 'wk': '♔',
            'bp': '♟', 'br': '♜', 'bn': '♞', 'bb': '♝', 'bq': '♛', 'bk': '♚'
        }

        for row in range(ROWS):
            for col in range(COLS):
                piece = self.board[row][col]
                if piece:
                    x = col * SQUARE_SIZE
                    y = row * SQUARE_SIZE

                    # Try to use image first
                    if self.piece_images.get(piece):
                        self.screen.blit(self.piece_images[piece], (x, y))
                    else:
                        # Fallback to symbol
                        symbol = piece_symbols.get(piece, piece)
                        text = pygame.font.Font(None, 48).render(symbol, True, TEXT_COLOR)
                        text_x = x + SQUARE_SIZE // 2 - text.get_width() // 2
                        text_y = y + SQUARE_SIZE // 2 - text.get_height() // 2
                        self.screen.blit(text, (text_x, text_y))
    
    def draw_sidebar(self, game_mode: int) -> List[Tuple[str, pygame.Rect]]:
        """Draw sidebar with game info and buttons."""
        sidebar_rect = pygame.Rect(BOARD_SIZE, 0, SIDEBAR_WIDTH, HEIGHT)
        pygame.draw.rect(self.screen, SIDEBAR_BG, sidebar_rect)
        
        y = 20
        
        # Game info
        mode_text = "Human vs Human" if game_mode == HUMAN_VS_HUMAN else "Human vs AI"
        texts = [
            "Chess Game",
            f"Mode: {mode_text}",
            f"Turn: {'White' if self.current_player == 'w' else 'Black'}",
            f"Moves: {self.move_count}"
        ]
        
        for text in texts:
            surface = self.font.render(text, True, TEXT_COLOR)
            self.screen.blit(surface, (BOARD_SIZE + 10, y))
            y += 30
        
        y += 20
        
        # Buttons
        buttons = []
        button_texts = ["New Game", "Back to Menu", "Exit"]
        button_colors = [SUCCESS_COLOR, BUTTON_COLOR, ERROR_COLOR]
        
        for i, (text, color) in enumerate(zip(button_texts, button_colors)):
            rect = pygame.Rect(BOARD_SIZE + 10, y, SIDEBAR_WIDTH - 20, 35)
            pygame.draw.rect(self.screen, color, rect)
            pygame.draw.rect(self.screen, TEXT_COLOR, rect, 2)
            
            text_surface = self.font.render(text, True, (255, 255, 255))
            text_rect = text_surface.get_rect(center=rect.center)
            self.screen.blit(text_surface, text_rect)
            
            buttons.append((["new_game", "menu", "exit"][i], rect))
            y += 45
        
        return buttons
    
    def show_menu(self) -> Optional[int]:
        """Show main menu."""
        while True:
            self.screen.fill((200, 200, 200))
            
            # Title
            title = pygame.font.Font(None, 72).render("Chess Game", True, TEXT_COLOR)
            title_rect = title.get_rect(center=(WIDTH // 2, 100))
            self.screen.blit(title, title_rect)
            
            # Buttons
            button1 = pygame.Rect(WIDTH // 2 - 150, 200, 300, 60)
            button2 = pygame.Rect(WIDTH // 2 - 150, 300, 300, 60)
            
            pygame.draw.rect(self.screen, BUTTON_COLOR, button1)
            pygame.draw.rect(self.screen, BUTTON_COLOR, button2)
            
            text1 = pygame.font.Font(None, 36).render("Human vs Human", True, (255, 255, 255))
            text2 = pygame.font.Font(None, 36).render("Human vs AI", True, (255, 255, 255))
            
            self.screen.blit(text1, text1.get_rect(center=button1.center))
            self.screen.blit(text2, text2.get_rect(center=button2.center))
            
            pygame.display.flip()
            
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return None
                if event.type == pygame.MOUSEBUTTONDOWN:
                    if button1.collidepoint(event.pos):
                        return HUMAN_VS_HUMAN
                    elif button2.collidepoint(event.pos):
                        return HUMAN_VS_AI
            
            self.clock.tick(60)

    def run_game(self, game_mode: int) -> str:
        """Run the main game loop."""
        self.reset_game()
        ai_thinking = False
        ai_move_time = 0

        while True:
            self.clock.tick(60)

            # Check game over
            if self.is_game_over():
                winner = "White" if self.current_player == 'b' else "Black"
                return self.show_game_over_dialog(f"{winner} wins!")

            # AI move
            if game_mode == HUMAN_VS_AI and self.current_player == 'b' and not ai_thinking:
                ai_thinking = True
                ai_move_time = pygame.time.get_ticks()

            if ai_thinking and pygame.time.get_ticks() - ai_move_time > 1000:
                ai_move = self.get_ai_move()
                if ai_move:
                    self.make_move(ai_move[0], ai_move[1])
                ai_thinking = False

            # Draw everything
            self.screen.fill((250, 250, 250))
            self.draw_board()
            buttons = self.draw_sidebar(game_mode)

            if ai_thinking:
                text = self.font.render("AI is thinking...", True, TEXT_COLOR)
                self.screen.blit(text, (BOARD_SIZE + 10, HEIGHT - 50))

            pygame.display.flip()

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return "exit"

                if event.type == pygame.MOUSEBUTTONDOWN and not ai_thinking:
                    x, y = event.pos

                    # Check button clicks
                    for button_type, rect in buttons:
                        if rect.collidepoint(event.pos):
                            if button_type == "new_game":
                                self.reset_game()
                            elif button_type == "menu":
                                return "menu"
                            elif button_type == "exit":
                                return "exit"

                    # Check board clicks
                    if x < BOARD_SIZE:
                        row, col = y // SQUARE_SIZE, x // SQUARE_SIZE
                        if 0 <= row < ROWS and 0 <= col < COLS:
                            # Only allow human moves
                            if game_mode == HUMAN_VS_HUMAN or (game_mode == HUMAN_VS_AI and self.current_player == 'w'):
                                if self.selected:
                                    if (row, col) in self.valid_moves:
                                        self.make_move(self.selected, (row, col))
                                    else:
                                        piece = self.board[row][col]
                                        if piece and piece[0] == self.current_player:
                                            self.selected = (row, col)
                                            self.valid_moves = self.get_valid_moves((row, col))
                                        else:
                                            self.selected = None
                                            self.valid_moves = []
                                else:
                                    piece = self.board[row][col]
                                    if piece and piece[0] == self.current_player:
                                        self.selected = (row, col)
                                        self.valid_moves = self.get_valid_moves((row, col))

    def show_game_over_dialog(self, message: str) -> str:
        """Show game over dialog."""
        while True:
            # Draw overlay
            overlay = pygame.Surface((WIDTH, HEIGHT))
            overlay.set_alpha(128)
            overlay.fill((0, 0, 0))
            self.screen.blit(overlay, (0, 0))

            # Dialog box
            dialog = pygame.Rect(WIDTH//2 - 200, HEIGHT//2 - 100, 400, 200)
            pygame.draw.rect(self.screen, (240, 240, 240), dialog)
            pygame.draw.rect(self.screen, (0, 0, 0), dialog, 3)

            # Message
            text = pygame.font.Font(None, 36).render(message, True, (0, 0, 0))
            text_rect = text.get_rect(center=(WIDTH//2, HEIGHT//2 - 30))
            self.screen.blit(text, text_rect)

            # Buttons
            play_again = pygame.Rect(WIDTH//2 - 180, HEIGHT//2 + 20, 110, 40)
            menu_btn = pygame.Rect(WIDTH//2 - 55, HEIGHT//2 + 20, 110, 40)
            exit_btn = pygame.Rect(WIDTH//2 + 70, HEIGHT//2 + 20, 110, 40)

            pygame.draw.rect(self.screen, SUCCESS_COLOR, play_again)
            pygame.draw.rect(self.screen, BUTTON_COLOR, menu_btn)
            pygame.draw.rect(self.screen, ERROR_COLOR, exit_btn)

            # Button text
            texts = ["Play Again", "Menu", "Exit"]
            rects = [play_again, menu_btn, exit_btn]

            for text, rect in zip(texts, rects):
                button_text = pygame.font.Font(None, 24).render(text, True, (255, 255, 255))
                text_rect = button_text.get_rect(center=rect.center)
                self.screen.blit(button_text, text_rect)

            pygame.display.flip()

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return "exit"
                if event.type == pygame.MOUSEBUTTONDOWN:
                    if play_again.collidepoint(event.pos):
                        return "play_again"
                    elif menu_btn.collidepoint(event.pos):
                        return "menu"
                    elif exit_btn.collidepoint(event.pos):
                        return "exit"

            self.clock.tick(60)

    def run(self):
        """Main application loop."""
        while True:
            game_mode = self.show_menu()
            if game_mode is None:
                break

            while True:
                result = self.run_game(game_mode)
                if result == "menu":
                    break
                elif result == "exit":
                    pygame.quit()
                    sys.exit()
                elif result == "play_again":
                    continue

        pygame.quit()
        sys.exit()

def main():
    """Main function."""
    game = ChessGame()
    game.run()

if __name__ == "__main__":
    main()
