"""
Clean Chess Game Implementation
A well-organized chess game with proper separation of concerns.
"""

import pygame
import sys
from enum import Enum
from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass

# ==================== CONSTANTS ====================

class GameMode(Enum):
    HUMAN_VS_HUMAN = 1
    HUMAN_VS_AI = 2

class Colors:
    WHITE = (240, 217, 181)
    BLACK = (181, 136, 99)
    HIGHLIGHT = (186, 202, 68)
    VALID_MOVE = (144, 238, 144)
    PIECE_WHITE = (255, 255, 255)
    PIECE_BLACK = (0, 0, 0)
    TEXT_COLOR = (50, 50, 50)
    MENU_BG = (200, 200, 200)
    BUTTON_COLOR = (70, 130, 180)
    BUTTON_HOVER = (100, 149, 237)
    BUTTON_DISABLED = (169, 169, 169)
    SIDEBAR_BG = (245, 245, 245)
    BORDER_COLOR = (100, 100, 100)
    SUCCESS_COLOR = (34, 139, 34)
    WARNING_COLOR = (255, 140, 0)
    ERROR_COLOR = (220, 20, 60)

class Config:
    BOARD_SIZE = 640
    SIDEBAR_WIDTH = 200
    WIDTH = BOARD_SIZE + SIDEBAR_WIDTH
    HEIGHT = BOARD_SIZE + 50
    ROWS, COLS = 8, 8
    SQUARE_SIZE = BOARD_SIZE // COLS
    TIMER_MINUTES = 30

PIECE_SYMBOLS = {
    'wp': '♙', 'wr': '♖', 'wn': '♘', 'wb': '♗', 'wq': '♕', 'wk': '♔',
    'bp': '♟', 'br': '♜', 'bn': '♞', 'bb': '♝', 'bq': '♛', 'bk': '♚'
}

PIECE_VALUES = {'p': 1, 'n': 3, 'b': 3, 'r': 5, 'q': 9, 'k': 100}

# ==================== DATA CLASSES ====================

@dataclass
class Move:
    from_pos: Tuple[int, int]
    to_pos: Tuple[int, int]
    piece: str
    captured_piece: str = ""
    board_before: List[List[str]] = None
    board_after: List[List[str]] = None

@dataclass
class GameState:
    board: List[List[str]]
    current_player: str
    selected: Optional[Tuple[int, int]]
    valid_moves: List[Tuple[int, int]]
    game_over: bool
    game_status: str
    move_count: int

# ==================== CORE CLASSES ====================

class MoveHistory:
    """Manages move history for undo/redo functionality."""
    
    def __init__(self):
        self.moves: List[Move] = []
        self.current_index = -1
    
    def add_move(self, move: Move):
        """Add a new move to history."""
        self.moves = self.moves[:self.current_index + 1]
        self.moves.append(move)
        self.current_index += 1
    
    def can_undo(self) -> bool:
        return self.current_index >= 0
    
    def can_redo(self) -> bool:
        return self.current_index < len(self.moves) - 1
    
    def undo(self) -> Optional[Move]:
        if self.can_undo():
            move = self.moves[self.current_index]
            self.current_index -= 1
            return move
        return None
    
    def redo(self) -> Optional[Move]:
        if self.can_redo():
            self.current_index += 1
            return self.moves[self.current_index]
        return None
    
    def clear(self):
        self.moves.clear()
        self.current_index = -1

class ChessTimer:
    """Manages game timers for both players."""
    
    def __init__(self, minutes: int = Config.TIMER_MINUTES):
        self.initial_time = minutes * 60 * 1000  # Convert to milliseconds
        self.white_time = self.initial_time
        self.black_time = self.initial_time
        self.last_update = 0
        self.active = False
        self.current_player = 'w'
    
    def start_timer(self, player: str):
        self.current_player = player
        self.active = True
        self.last_update = pygame.time.get_ticks()
    
    def pause_timer(self):
        if self.active:
            self.update_time()
            self.active = False
    
    def switch_player(self, new_player: str):
        if self.active:
            self.update_time()
        self.current_player = new_player
        self.last_update = pygame.time.get_ticks()
    
    def update_time(self):
        if not self.active:
            return
        
        current_time = pygame.time.get_ticks()
        elapsed = current_time - self.last_update
        
        if self.current_player == 'w':
            self.white_time = max(0, self.white_time - elapsed)
        else:
            self.black_time = max(0, self.black_time - elapsed)
        
        self.last_update = current_time
    
    def get_time_string(self, player: str) -> str:
        time_ms = self.white_time if player == 'w' else self.black_time
        total_seconds = time_ms // 1000
        minutes = total_seconds // 60
        seconds = total_seconds % 60
        return f"{minutes:02d}:{seconds:02d}"
    
    def is_time_up(self, player: str) -> bool:
        time_ms = self.white_time if player == 'w' else self.black_time
        return time_ms <= 0
    
    def get_time_color(self, player: str) -> Tuple[int, int, int]:
        time_ms = self.white_time if player == 'w' else self.black_time
        total_seconds = time_ms // 1000
        
        if total_seconds <= 30:
            return Colors.ERROR_COLOR
        elif total_seconds <= 60:
            return Colors.WARNING_COLOR
        else:
            return Colors.SUCCESS_COLOR
    
    def reset(self, minutes: int = Config.TIMER_MINUTES):
        self.initial_time = minutes * 60 * 1000
        self.white_time = self.initial_time
        self.black_time = self.initial_time
        self.active = False
        self.current_player = 'w'

class Board:
    """Manages the chess board and piece logic."""
    
    def __init__(self):
        self.board = self.create_initial_board()
    
    def create_initial_board(self) -> List[List[str]]:
        """Create the standard chess starting position."""
        return [
            ["br", "bn", "bb", "bq", "bk", "bb", "bn", "br"],
            ["bp"] * 8,
            [""] * 8,
            [""] * 8,
            [""] * 8,
            [""] * 8,
            ["wp"] * 8,
            ["wr", "wn", "wb", "wq", "wk", "wb", "wn", "wr"]
        ]
    
    def get_piece(self, row: int, col: int) -> str:
        """Get piece at position."""
        if 0 <= row < Config.ROWS and 0 <= col < Config.COLS:
            return self.board[row][col]
        return ""
    
    def set_piece(self, row: int, col: int, piece: str):
        """Set piece at position."""
        if 0 <= row < Config.ROWS and 0 <= col < Config.COLS:
            self.board[row][col] = piece
    
    def make_move(self, from_pos: Tuple[int, int], to_pos: Tuple[int, int]) -> Move:
        """Make a move and return move object."""
        from_row, from_col = from_pos
        to_row, to_col = to_pos
        
        piece = self.get_piece(from_row, from_col)
        captured = self.get_piece(to_row, to_col)
        
        move = Move(
            from_pos=from_pos,
            to_pos=to_pos,
            piece=piece,
            captured_piece=captured,
            board_before=[row[:] for row in self.board],
        )
        
        self.set_piece(to_row, to_col, piece)
        self.set_piece(from_row, from_col, "")
        
        move.board_after = [row[:] for row in self.board]
        return move
    
    def undo_move(self, move: Move):
        """Undo a move."""
        self.board = [row[:] for row in move.board_before]
    
    def copy(self) -> List[List[str]]:
        """Return a copy of the board."""
        return [row[:] for row in self.board]
    
    def reset(self):
        """Reset board to initial position."""
        self.board = self.create_initial_board()

class MoveValidator:
    """Handles move validation logic."""

    @staticmethod
    def is_valid_move(board: List[List[str]], from_pos: Tuple[int, int], to_pos: Tuple[int, int]) -> bool:
        """Check if a move is valid."""
        from_row, from_col = from_pos
        to_row, to_col = to_pos

        # Basic bounds checking
        if not (0 <= to_row < Config.ROWS and 0 <= to_col < Config.COLS):
            return False

        piece = board[from_row][from_col]
        if not piece:
            return False

        target = board[to_row][to_col]
        if target and target[0] == piece[0]:  # Can't capture own piece
            return False

        piece_type = piece[1]
        row_diff = to_row - from_row
        col_diff = to_col - from_col

        return MoveValidator._validate_piece_move(board, piece_type, piece[0], from_pos, to_pos, row_diff, col_diff)

    @staticmethod
    def _validate_piece_move(board: List[List[str]], piece_type: str, color: str,
                           from_pos: Tuple[int, int], to_pos: Tuple[int, int],
                           row_diff: int, col_diff: int) -> bool:
        """Validate move for specific piece type."""
        from_row, from_col = from_pos
        to_row, to_col = to_pos
        target = board[to_row][to_col]

        if piece_type == 'p':  # Pawn
            direction = -1 if color == 'w' else 1
            start_row = 6 if color == 'w' else 1

            if col_diff == 0:  # Forward move
                if row_diff == direction and not target:
                    return True
                if from_row == start_row and row_diff == 2 * direction and not target:
                    return True
            elif abs(col_diff) == 1 and row_diff == direction and target:  # Diagonal capture
                return True

        elif piece_type == 'r':  # Rook
            if row_diff == 0 or col_diff == 0:
                return MoveValidator._is_path_clear(board, from_pos, to_pos)

        elif piece_type == 'b':  # Bishop
            if abs(row_diff) == abs(col_diff):
                return MoveValidator._is_path_clear(board, from_pos, to_pos)

        elif piece_type == 'q':  # Queen
            if row_diff == 0 or col_diff == 0 or abs(row_diff) == abs(col_diff):
                return MoveValidator._is_path_clear(board, from_pos, to_pos)

        elif piece_type == 'k':  # King
            return abs(row_diff) <= 1 and abs(col_diff) <= 1

        elif piece_type == 'n':  # Knight
            return (abs(row_diff) == 2 and abs(col_diff) == 1) or (abs(row_diff) == 1 and abs(col_diff) == 2)

        return False

    @staticmethod
    def _is_path_clear(board: List[List[str]], from_pos: Tuple[int, int], to_pos: Tuple[int, int]) -> bool:
        """Check if path is clear for sliding pieces."""
        from_row, from_col = from_pos
        to_row, to_col = to_pos

        row_step = 0 if from_row == to_row else (1 if to_row > from_row else -1)
        col_step = 0 if from_col == to_col else (1 if to_col > from_col else -1)

        current_row, current_col = from_row + row_step, from_col + col_step

        while current_row != to_row or current_col != to_col:
            if board[current_row][current_col]:
                return False
            current_row += row_step
            current_col += col_step

        return True

    @staticmethod
    def get_valid_moves(board: List[List[str]], from_pos: Tuple[int, int]) -> List[Tuple[int, int]]:
        """Get all valid moves for a piece."""
        valid_moves = []
        for row in range(Config.ROWS):
            for col in range(Config.COLS):
                if MoveValidator.is_valid_move(board, from_pos, (row, col)):
                    valid_moves.append((row, col))
        return valid_moves

    @staticmethod
    def get_all_moves_for_player(board: List[List[str]], player: str) -> List[Tuple[Tuple[int, int], Tuple[int, int]]]:
        """Get all valid moves for a player."""
        moves = []
        for row in range(Config.ROWS):
            for col in range(Config.COLS):
                piece = board[row][col]
                if piece and piece[0] == player:
                    valid_moves = MoveValidator.get_valid_moves(board, (row, col))
                    for to_pos in valid_moves:
                        moves.append(((row, col), to_pos))
        return moves

class GameLogic:
    """Handles game state and rules."""

    @staticmethod
    def evaluate_board(board: List[List[str]]) -> float:
        """Evaluate board position for AI."""
        score = 0
        for row in range(Config.ROWS):
            for col in range(Config.COLS):
                piece = board[row][col]
                if piece:
                    value = PIECE_VALUES[piece[1]]
                    # Small positional bonus for center control
                    if 2 <= row <= 5 and 2 <= col <= 5:
                        value += 0.1

                    if piece[0] == 'w':
                        score += value
                    else:
                        score -= value
        return score

    @staticmethod
    def is_checkmate(board: List[List[str]], player: str) -> bool:
        """Check if player is in checkmate (simplified)."""
        # Find king
        for row in range(Config.ROWS):
            for col in range(Config.COLS):
                piece = board[row][col]
                if piece == f'{player}k':
                    return False
        return True  # King captured = checkmate

    @staticmethod
    def is_stalemate(board: List[List[str]], player: str) -> bool:
        """Check if player is in stalemate."""
        moves = MoveValidator.get_all_moves_for_player(board, player)
        return len(moves) == 0 and not GameLogic.is_checkmate(board, player)

    @staticmethod
    def get_game_status(board: List[List[str]], current_player: str) -> str:
        """Get current game status."""
        if GameLogic.is_checkmate(board, current_player):
            winner = "Black" if current_player == 'w' else "White"
            return f"CHECKMATE! {winner} wins!"
        elif GameLogic.is_stalemate(board, current_player):
            return "STALEMATE! It's a draw!"
        return ""

class ChessAI:
    """AI player using minimax algorithm."""

    @staticmethod
    def get_best_move(board: List[List[str]], depth: int = 2) -> Optional[Tuple[Tuple[int, int], Tuple[int, int]]]:
        """Get best move for AI (black player)."""
        import random

        moves = MoveValidator.get_all_moves_for_player(board, 'b')
        if not moves:
            return None

        random.shuffle(moves)  # Add randomness
        best_move = None
        best_score = float('inf')

        for move in moves:
            from_pos, to_pos = move
            from_row, from_col = from_pos
            to_row, to_col = to_pos

            # Make move
            temp_piece = board[to_row][to_col]
            board[to_row][to_col] = board[from_row][from_col]
            board[from_row][from_col] = ""

            # Evaluate
            score = ChessAI._minimax(board, depth - 1, True, float('-inf'), float('inf'))

            # Undo move
            board[from_row][from_col] = board[to_row][to_col]
            board[to_row][to_col] = temp_piece

            if score < best_score:
                best_score = score
                best_move = move

        return best_move

    @staticmethod
    def _minimax(board: List[List[str]], depth: int, maximizing: bool, alpha: float, beta: float) -> float:
        """Minimax algorithm with alpha-beta pruning."""
        if depth == 0:
            return GameLogic.evaluate_board(board)

        player = 'w' if maximizing else 'b'
        moves = MoveValidator.get_all_moves_for_player(board, player)

        if maximizing:
            max_eval = float('-inf')
            for move in moves:
                from_pos, to_pos = move
                from_row, from_col = from_pos
                to_row, to_col = to_pos

                temp_piece = board[to_row][to_col]
                board[to_row][to_col] = board[from_row][from_col]
                board[from_row][from_col] = ""

                eval_score = ChessAI._minimax(board, depth - 1, False, alpha, beta)

                board[from_row][from_col] = board[to_row][to_col]
                board[to_row][to_col] = temp_piece

                max_eval = max(max_eval, eval_score)
                alpha = max(alpha, eval_score)
                if beta <= alpha:
                    break
            return max_eval
        else:
            min_eval = float('inf')
            for move in moves:
                from_pos, to_pos = move
                from_row, from_col = from_pos
                to_row, to_col = to_pos

                temp_piece = board[to_row][to_col]
                board[to_row][to_col] = board[from_row][from_col]
                board[from_row][from_col] = ""

                eval_score = ChessAI._minimax(board, depth - 1, True, alpha, beta)

                board[from_row][from_col] = board[to_row][to_col]
                board[to_row][to_col] = temp_piece

                min_eval = min(min_eval, eval_score)
                beta = min(beta, eval_score)
                if beta <= alpha:
                    break
            return min_eval

class Renderer:
    """Handles all rendering operations."""

    def __init__(self, screen: pygame.Surface):
        self.screen = screen
        self.fonts = {
            'title': pygame.font.Font(None, 72),
            'large': pygame.font.Font(None, 48),
            'medium': pygame.font.Font(None, 32),
            'small': pygame.font.Font(None, 24),
            'timer': pygame.font.Font(None, 32)
        }
        self.piece_images = {}
        self._load_piece_images()

    def _load_piece_images(self):
        """Load piece images with fallback."""
        pieces = ['wp', 'wr', 'wn', 'wb', 'wq', 'wk', 'bp', 'br', 'bn', 'bb', 'bq', 'bk']
        for piece in pieces:
            try:
                image = pygame.image.load(f"pieces/{piece}.png")
                self.piece_images[piece] = pygame.transform.scale(image, (Config.SQUARE_SIZE, Config.SQUARE_SIZE))
            except pygame.error:
                self.piece_images[piece] = None

    def draw_board(self, board: List[List[str]], selected: Optional[Tuple[int, int]],
                   valid_moves: List[Tuple[int, int]]):
        """Draw the chess board with pieces and highlights."""
        # Draw squares
        for row in range(Config.ROWS):
            for col in range(Config.COLS):
                color = Colors.WHITE if (row + col) % 2 == 0 else Colors.BLACK
                rect = pygame.Rect(col * Config.SQUARE_SIZE, row * Config.SQUARE_SIZE,
                                 Config.SQUARE_SIZE, Config.SQUARE_SIZE)
                pygame.draw.rect(self.screen, color, rect)

        # Draw valid moves
        for row, col in valid_moves:
            rect = pygame.Rect(col * Config.SQUARE_SIZE, row * Config.SQUARE_SIZE,
                             Config.SQUARE_SIZE, Config.SQUARE_SIZE)
            pygame.draw.rect(self.screen, Colors.VALID_MOVE, rect, 3)

        # Draw selected piece highlight
        if selected:
            row, col = selected
            rect = pygame.Rect(col * Config.SQUARE_SIZE, row * Config.SQUARE_SIZE,
                             Config.SQUARE_SIZE, Config.SQUARE_SIZE)
            pygame.draw.rect(self.screen, Colors.HIGHLIGHT, rect, 4)

        # Draw pieces
        for row in range(Config.ROWS):
            for col in range(Config.COLS):
                piece = board[row][col]
                if piece:
                    if self.piece_images.get(piece):
                        self.screen.blit(self.piece_images[piece],
                                       (col * Config.SQUARE_SIZE, row * Config.SQUARE_SIZE))
                    else:
                        self._draw_piece_fallback(piece, col * Config.SQUARE_SIZE, row * Config.SQUARE_SIZE)

        # Draw board border
        pygame.draw.rect(self.screen, Colors.BORDER_COLOR, (0, 0, Config.BOARD_SIZE, Config.BOARD_SIZE), 3)

        # Draw coordinates
        for col in range(Config.COLS):
            letter = chr(ord('a') + col)
            text = self.fonts['small'].render(letter, True, Colors.TEXT_COLOR)
            self.screen.blit(text, (col * Config.SQUARE_SIZE + Config.SQUARE_SIZE - 15, Config.BOARD_SIZE + 5))

        for row in range(Config.ROWS):
            number = str(8 - row)
            text = self.fonts['small'].render(number, True, Colors.TEXT_COLOR)
            self.screen.blit(text, (5, row * Config.SQUARE_SIZE + 5))

    def _draw_piece_fallback(self, piece: str, x: int, y: int):
        """Draw piece using simple shapes as fallback."""
        center_x = x + Config.SQUARE_SIZE // 2
        center_y = y + Config.SQUARE_SIZE // 2
        piece_color = Colors.PIECE_WHITE if piece[0] == 'w' else Colors.PIECE_BLACK
        outline_color = Colors.PIECE_BLACK if piece[0] == 'w' else Colors.PIECE_WHITE

        # Simple circle for all pieces with different sizes
        size_map = {'p': 12, 'r': 15, 'n': 14, 'b': 13, 'q': 16, 'k': 17}
        size = size_map.get(piece[1], 12)

        pygame.draw.circle(self.screen, piece_color, (center_x, center_y), size)
        pygame.draw.circle(self.screen, outline_color, (center_x, center_y), size, 2)

        # Draw piece symbol
        symbol = PIECE_SYMBOLS.get(piece, piece[1].upper())
        text = self.fonts['small'].render(symbol, True, outline_color)
        text_rect = text.get_rect(center=(center_x, center_y))
        self.screen.blit(text, text_rect)

    def draw_sidebar(self, game_state: GameState, game_mode: GameMode, timer: ChessTimer,
                     history: MoveHistory, ai_thinking: bool) -> List[Tuple[str, pygame.Rect, bool]]:
        """Draw the game sidebar with controls."""
        sidebar_rect = pygame.Rect(Config.BOARD_SIZE, 0, Config.SIDEBAR_WIDTH, Config.HEIGHT)
        pygame.draw.rect(self.screen, Colors.SIDEBAR_BG, sidebar_rect)
        pygame.draw.rect(self.screen, Colors.BORDER_COLOR, sidebar_rect, 2)

        y_pos = 20
        buttons = []

        # Title
        title = self.fonts['medium'].render("Chess Game", True, Colors.TEXT_COLOR)
        self.screen.blit(title, (Config.BOARD_SIZE + 10, y_pos))
        y_pos += 40

        # Game mode
        mode_text = "Human vs Human" if game_mode == GameMode.HUMAN_VS_HUMAN else "Human vs AI"
        mode = self.fonts['small'].render(f"Mode: {mode_text}", True, Colors.TEXT_COLOR)
        self.screen.blit(mode, (Config.BOARD_SIZE + 10, y_pos))
        y_pos += 30

        # Current player
        if ai_thinking:
            player_text = "AI is thinking..."
            color = Colors.WARNING_COLOR
        else:
            player_name = "White" if game_state.current_player == 'w' else "Black"
            if game_mode == GameMode.HUMAN_VS_AI and game_state.current_player == 'b':
                player_name += " (AI)"
            player_text = f"Turn: {player_name}"
            color = Colors.TEXT_COLOR

        player = self.fonts['small'].render(player_text, True, color)
        self.screen.blit(player, (Config.BOARD_SIZE + 10, y_pos))
        y_pos += 30

        # Move count
        moves = self.fonts['small'].render(f"Moves: {game_state.move_count}", True, Colors.TEXT_COLOR)
        self.screen.blit(moves, (Config.BOARD_SIZE + 10, y_pos))
        y_pos += 40

        # Timers
        y_pos = self._draw_timers(timer, y_pos)

        # Game status
        if game_state.game_status:
            y_pos = self._draw_game_status(game_state.game_status, y_pos)

        # Control buttons
        buttons = self._draw_control_buttons(y_pos, history, ai_thinking)

        return buttons

    def _draw_timers(self, timer: ChessTimer, y_pos: int) -> int:
        """Draw timer display."""
        timer_title = self.fonts['small'].render("Game Timers:", True, Colors.TEXT_COLOR)
        self.screen.blit(timer_title, (Config.BOARD_SIZE + 10, y_pos))
        y_pos += 25

        # White timer
        white_time = timer.get_time_string('w')
        white_color = timer.get_time_color('w')
        white_active = timer.current_player == 'w' and timer.active

        white_label = self.fonts['small'].render("White:", True, Colors.TEXT_COLOR)
        white_time_text = self.fonts['timer'].render(white_time, True, white_color)

        self.screen.blit(white_label, (Config.BOARD_SIZE + 10, y_pos))
        self.screen.blit(white_time_text, (Config.BOARD_SIZE + 70, y_pos - 3))

        if white_active:
            pygame.draw.circle(self.screen, Colors.SUCCESS_COLOR, (Config.BOARD_SIZE + 170, y_pos + 10), 5)

        y_pos += 30

        # Black timer
        black_time = timer.get_time_string('b')
        black_color = timer.get_time_color('b')
        black_active = timer.current_player == 'b' and timer.active

        black_label = self.fonts['small'].render("Black:", True, Colors.TEXT_COLOR)
        black_time_text = self.fonts['timer'].render(black_time, True, black_color)

        self.screen.blit(black_label, (Config.BOARD_SIZE + 10, y_pos))
        self.screen.blit(black_time_text, (Config.BOARD_SIZE + 70, y_pos - 3))

        if black_active:
            pygame.draw.circle(self.screen, Colors.SUCCESS_COLOR, (Config.BOARD_SIZE + 170, y_pos + 10), 5)

        return y_pos + 40

    def _draw_game_status(self, status: str, y_pos: int) -> int:
        """Draw game status messages."""
        if "CHECKMATE" in status:
            color = Colors.ERROR_COLOR
        elif "STALEMATE" in status:
            color = Colors.WARNING_COLOR
        elif "TIME UP" in status:
            color = Colors.ERROR_COLOR
        else:
            color = Colors.TEXT_COLOR

        # Word wrap for long status messages
        words = status.split()
        lines = []
        current_line = ""
        for word in words:
            test_line = current_line + " " + word if current_line else word
            if self.fonts['small'].size(test_line)[0] < Config.SIDEBAR_WIDTH - 20:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        if current_line:
            lines.append(current_line)

        for line in lines:
            text = self.fonts['small'].render(line, True, color)
            self.screen.blit(text, (Config.BOARD_SIZE + 10, y_pos))
            y_pos += 25

        return y_pos + 20

    def _draw_control_buttons(self, y_pos: int, history: MoveHistory, ai_thinking: bool) -> List[Tuple[str, pygame.Rect, bool]]:
        """Draw control buttons."""
        button_width = Config.SIDEBAR_WIDTH - 20
        button_height = 35
        button_spacing = 10
        buttons = []

        button_configs = [
            ("Undo Move", "undo", Colors.BUTTON_COLOR, history.can_undo() and not ai_thinking),
            ("Redo Move", "redo", Colors.BUTTON_COLOR, history.can_redo() and not ai_thinking),
            ("New Game", "new_game", Colors.SUCCESS_COLOR, True),
            ("Back to Menu", "menu", Colors.BUTTON_COLOR, True),
            ("Exit Game", "exit", Colors.ERROR_COLOR, True)
        ]

        for text, action, base_color, enabled in button_configs:
            rect = pygame.Rect(Config.BOARD_SIZE + 10, y_pos, button_width, button_height)
            color = base_color if enabled else Colors.BUTTON_DISABLED

            pygame.draw.rect(self.screen, color, rect)
            pygame.draw.rect(self.screen, Colors.BORDER_COLOR, rect, 2)

            text_color = Colors.PIECE_WHITE if enabled else Colors.TEXT_COLOR
            button_text = self.fonts['small'].render(text, True, text_color)
            text_rect = button_text.get_rect(center=rect.center)
            self.screen.blit(button_text, text_rect)
            buttons.append((action, rect, enabled))
            y_pos += button_height + button_spacing
        return buttons

    def draw_menu(self) -> Tuple[pygame.Rect, pygame.Rect]:
        """Draw the main menu."""
        self.screen.fill(Colors.MENU_BG)

        # Title
        title = self.fonts['title'].render("Enhanced Chess Game", True, Colors.TEXT_COLOR)
        title_rect = title.get_rect(center=(Config.WIDTH // 2, 80))
        self.screen.blit(title, title_rect)

        # Buttons
        button1_rect = pygame.Rect(Config.WIDTH // 2 - 150, 200, 300, 60)
        button2_rect = pygame.Rect(Config.WIDTH // 2 - 150, 300, 300, 60)

        pygame.draw.rect(self.screen, Colors.BUTTON_COLOR, button1_rect)
        pygame.draw.rect(self.screen, Colors.BUTTON_COLOR, button2_rect)
        pygame.draw.rect(self.screen, Colors.TEXT_COLOR, button1_rect, 3)
        pygame.draw.rect(self.screen, Colors.TEXT_COLOR, button2_rect, 3)

        # Button text
        text1 = self.fonts['large'].render("Human vs Human", True, Colors.TEXT_COLOR)
        text2 = self.fonts['large'].render("Human vs AI", True, Colors.TEXT_COLOR)

        text1_rect = text1.get_rect(center=button1_rect.center)
        text2_rect = text2.get_rect(center=button2_rect.center)

        self.screen.blit(text1, text1_rect)
        self.screen.blit(text2, text2_rect)

        return button1_rect, button2_rect

class DialogManager:
    """Manages game dialogs."""

    def __init__(self, screen: pygame.Surface, renderer: Renderer):
        self.screen = screen
        self.renderer = renderer

    def show_game_over_dialog(self, message: str) -> Tuple[pygame.Rect, pygame.Rect, pygame.Rect]:
        """Show game over dialog with three options."""
        overlay = pygame.Surface((Config.WIDTH, Config.HEIGHT))
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        self.screen.blit(overlay, (0, 0))

        # Dialog box
        dialog_width, dialog_height = 450, 200
        dialog_x = (Config.WIDTH - dialog_width) // 2
        dialog_y = (Config.HEIGHT - dialog_height) // 2

        dialog_rect = pygame.Rect(dialog_x, dialog_y, dialog_width, dialog_height)
        pygame.draw.rect(self.screen, (240, 240, 240), dialog_rect)
        pygame.draw.rect(self.screen, (0, 0, 0), dialog_rect, 3)

        # Title
        title = self.renderer.fonts['medium'].render(message, True, (0, 0, 0))
        title_rect = title.get_rect(center=(dialog_x + dialog_width//2, dialog_y + 50))
        self.screen.blit(title, title_rect)

        # Buttons
        button_width, button_height = 110, 40
        button_y = dialog_y + 120
        button_spacing = 15

        play_again_rect = pygame.Rect(dialog_x + 20, button_y, button_width, button_height)
        menu_rect = pygame.Rect(dialog_x + 20 + button_width + button_spacing, button_y, button_width, button_height)
        exit_rect = pygame.Rect(dialog_x + 20 + 2 * (button_width + button_spacing), button_y, button_width, button_height)

        # Draw buttons
        pygame.draw.rect(self.screen, (100, 200, 100), play_again_rect)
        pygame.draw.rect(self.screen, (100, 149, 237), menu_rect)
        pygame.draw.rect(self.screen, (200, 100, 100), exit_rect)

        for rect in [play_again_rect, menu_rect, exit_rect]:
            pygame.draw.rect(self.screen, (0, 0, 0), rect, 2)

        # Button text
        texts = ["Play Again", "Back to Menu", "Exit Game"]
        rects = [play_again_rect, menu_rect, exit_rect]

        for text, rect in zip(texts, rects):
            button_text = self.renderer.fonts['small'].render(text, True, (0, 0, 0))
            text_rect = button_text.get_rect(center=rect.center)
            self.screen.blit(button_text, text_rect)

        pygame.display.flip()
        return play_again_rect, menu_rect, exit_rect

    def show_exit_confirmation(self) -> Tuple[pygame.Rect, pygame.Rect, pygame.Rect]:
        """Show exit confirmation dialog."""
        overlay = pygame.Surface((Config.WIDTH, Config.HEIGHT))
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        self.screen.blit(overlay, (0, 0))

        # Dialog box
        dialog_width, dialog_height = 400, 180
        dialog_x = (Config.WIDTH - dialog_width) // 2
        dialog_y = (Config.HEIGHT - dialog_height) // 2

        dialog_rect = pygame.Rect(dialog_x, dialog_y, dialog_width, dialog_height)
        pygame.draw.rect(self.screen, (240, 240, 240), dialog_rect)
        pygame.draw.rect(self.screen, (0, 0, 0), dialog_rect, 3)

        # Title
        title = self.renderer.fonts['medium'].render("What would you like to do?", True, (0, 0, 0))
        title_rect = title.get_rect(center=(dialog_x + dialog_width//2, dialog_y + 35))
        self.screen.blit(title, title_rect)

        # Buttons
        button_width, button_height = 100, 35
        button_y = dialog_y + 80
        button_spacing = 20

        menu_rect = pygame.Rect(dialog_x + 30, button_y, button_width, button_height)
        exit_rect = pygame.Rect(dialog_x + 30 + button_width + button_spacing, button_y, button_width, button_height)
        cancel_rect = pygame.Rect(dialog_x + 30 + 2 * (button_width + button_spacing), button_y, button_width, button_height)

        # Draw buttons
        pygame.draw.rect(self.screen, (100, 149, 237), menu_rect)
        pygame.draw.rect(self.screen, (220, 100, 100), exit_rect)
        pygame.draw.rect(self.screen, (100, 200, 100), cancel_rect)

        for rect in [menu_rect, exit_rect, cancel_rect]:
            pygame.draw.rect(self.screen, (0, 0, 0), rect, 2)

        # Button text
        texts = ["Back to Menu", "Exit Game", "Cancel"]
        rects = [menu_rect, exit_rect, cancel_rect]

        for text, rect in zip(texts, rects):
            button_text = self.renderer.fonts['small'].render(text, True, (255, 255, 255))
            text_rect = button_text.get_rect(center=rect.center)
            self.screen.blit(button_text, text_rect)

        pygame.display.flip()
        return menu_rect, exit_rect, cancel_rect

class ChessGame:
    """Main game class that orchestrates all components."""

    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode((Config.WIDTH, Config.HEIGHT))
        pygame.display.set_caption("Enhanced Chess Game")
        self.clock = pygame.time.Clock()

        # Initialize components
        self.renderer = Renderer(self.screen)
        self.dialog_manager = DialogManager(self.screen, self.renderer)
        self.board = Board()
        self.timer = ChessTimer()
        self.history = MoveHistory()

        # Game state
        self.game_state = GameState(
            board=self.board.board,
            current_player='w',
            selected=None,
            valid_moves=[],
            game_over=False,
            game_status="",
            move_count=0
        )

        # UI state
        self.show_dialog = False
        self.show_exit_dialog = False
        self.ai_thinking = False
        self.ai_move_time = 0

    def show_menu(self) -> Optional[GameMode]:
        """Show main menu and return selected game mode."""
        while True:
            button1_rect, button2_rect = self.renderer.draw_menu()
            pygame.display.flip()

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return None

                if event.type == pygame.MOUSEBUTTONDOWN:
                    mouse_pos = pygame.mouse.get_pos()
                    if button1_rect.collidepoint(mouse_pos):
                        return GameMode.HUMAN_VS_HUMAN
                    elif button2_rect.collidepoint(mouse_pos):
                        return GameMode.HUMAN_VS_AI

            self.clock.tick(60)

    def run_game(self, game_mode: GameMode) -> str:
        """Run the main game loop."""
        self.reset_game()
        running = True

        while running:
            self.clock.tick(60)

            # Update timer
            if not self.game_state.game_over and not self.show_dialog and not self.show_exit_dialog:
                self.timer.update_time()

                # Check for time up
                if self.timer.is_time_up(self.game_state.current_player):
                    winner = "Black" if self.game_state.current_player == 'w' else "White"
                    self.game_state.game_status = f"TIME UP! {winner} wins!"
                    self.game_state.game_over = True
                    self.show_dialog = True
                    self.timer.pause_timer()

            # Check game over conditions
            if not self.game_state.game_over:
                self.game_state.game_status = GameLogic.get_game_status(self.board.board, self.game_state.current_player)
                if "CHECKMATE" in self.game_state.game_status or "STALEMATE" in self.game_state.game_status:
                    self.game_state.game_over = True
                    self.show_dialog = True
                    self.timer.pause_timer()

            # Handle AI move
            if (not self.game_state.game_over and game_mode == GameMode.HUMAN_VS_AI and
                self.game_state.current_player == 'b'):
                result = self.handle_ai_move()
                if result:
                    return result

            # Render everything
            self.render_game(game_mode)

            # Handle events
            result = self.handle_events(game_mode)
            if result:
                return result

        return "exit"

    def reset_game(self):
        """Reset game to initial state."""
        self.board.reset()
        self.timer.reset()
        self.history.clear()

        self.game_state = GameState(
            board=self.board.board,
            current_player='w',
            selected=None,
            valid_moves=[],
            game_over=False,
            game_status="",
            move_count=0
        )

        self.show_dialog = False
        self.show_exit_dialog = False
        self.ai_thinking = False
        self.timer.start_timer('w')
